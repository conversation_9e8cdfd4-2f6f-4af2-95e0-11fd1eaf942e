/*
  # Add summary field to recordings table

  1. Changes
    - Add `summary` column to `recordings` table to store conversation summaries
    - Column type is TEXT to store long-form summaries
    - Column is nullable since summary may not be available immediately

  2. Security
    - Maintain existing RLS policies
*/

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'recordings' 
    AND column_name = 'summary'
  ) THEN
    ALTER TABLE recordings 
    ADD COLUMN summary TEXT;
  END IF;
END $$;