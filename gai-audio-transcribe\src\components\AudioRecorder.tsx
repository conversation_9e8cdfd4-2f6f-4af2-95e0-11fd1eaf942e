import React, { useState, useRef, useEffect } from "react";
import { Mic, Square, Play, Pause, Upload } from "lucide-react";
import { saveRecording } from "../services/recordingService";
import type { Recording } from "../services/recordingService";
import { transcribeAudio } from "../services/transcriptionService";
import { supabase } from "../lib/supabase";

interface AudioRecorderProps {
  onRecordingComplete: (recording: Recording) => void;
}

const AudioRecorder: React.FC<AudioRecorderProps> = ({
  onRecordingComplete,
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [audioURL, setAudioURL] = useState<string | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [recordingDuration, setRecordingDuration] = useState(0);
  const [volume, setVolume] = useState(0);
  const [isDragging, setIsDragging] = useState(false);
  const [recordingName, setRecordingName] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  const mediaRecorderRef = useRef<MediaRecorder | null>(null);
  const audioChunksRef = useRef<Blob[]>([]);
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const animationFrameRef = useRef<number>();
  const recordingStartTimeRef = useRef<number>(0);
  const timerRef = useRef<number>();
  const fileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (timerRef.current) {
        cancelAnimationFrame(timerRef.current);
      }
    };
  }, []);

  const processAudioFile = async (file: File) => {
    const audioUrl = URL.createObjectURL(file);
    setAudioURL(audioUrl);
    setIsProcessing(true);

    try {
      const recording = await saveRecording({
        name: recordingName,
        status: "processing",
        transcription: null,
        tone: null,
        rating: null,
        audioBlob: file,
      });

      onRecordingComplete(recording);

      const updatedRecording = await transcribeAudio(
        file,
        recording.audio_url,
        recording.id,
        recording.name,
        file.type
      );

      if (updatedRecording) {
        onRecordingComplete(updatedRecording);
      }
    } catch (error) {
      console.error("Error processing audio file:", error);
      alert("Error processing audio file. Please try again.");
    } finally {
      setIsProcessing(false);
    }
  };

  const startRecording = async () => {
    if (!recordingName.trim()) {
      alert("Please enter a recording name");
      return;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      mediaRecorderRef.current = new MediaRecorder(stream);
      audioChunksRef.current = [];
      setRecordingDuration(0);

      mediaRecorderRef.current.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorderRef.current.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, {
          type: "audio/wav",
        });
        const audioUrl = URL.createObjectURL(audioBlob);
        setAudioURL(audioUrl);
        setIsProcessing(true);

        try {
          const recording = await saveRecording({
            name: recordingName,
            status: "processing",
            transcription: null,
            tone: null,
            rating: null,
            audioBlob,
          });

          onRecordingComplete(recording);

          const updatedRecording = await transcribeAudio(
            audioBlob,
            recording.audio_url,
            recording.id,
            recording.name,
            "audio/wav"
          );

          if (updatedRecording) {
            onRecordingComplete(updatedRecording);
          }
        } catch (error) {
          console.error("Error saving recording:", error);
          alert("Error saving recording. Please try again.");
        } finally {
          setIsProcessing(false);
        }
      };

      mediaRecorderRef.current.start();
      setIsRecording(true);
      recordingStartTimeRef.current = Date.now();
      updateRecordingDuration();

      const audioContext = new AudioContext();
      const analyser = audioContext.createAnalyser();
      const source = audioContext.createMediaStreamSource(stream);
      source.connect(analyser);
      analyser.fftSize = 256;
      const dataArray = new Uint8Array(analyser.frequencyBinCount);

      const updateVolume = () => {
        if (!isRecording) return;
        analyser.getByteFrequencyData(dataArray);
        const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
        setVolume(average / 128);
        animationFrameRef.current = requestAnimationFrame(updateVolume);
      };
      updateVolume();
    } catch (error) {
      console.error("Error accessing microphone:", error);
      alert("Error accessing microphone. Please check your permissions.");
    }
  };

  const updateRecordingDuration = () => {
    if (!isRecording) return;

    const update = () => {
      const duration = Math.floor(
        (Date.now() - recordingStartTimeRef.current) / 1000
      );
      setRecordingDuration(duration);
      timerRef.current = requestAnimationFrame(update);
    };

    update();
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current.stream
        .getTracks()
        .forEach((track) => track.stop());
      setIsRecording(false);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      if (timerRef.current) {
        cancelAnimationFrame(timerRef.current);
      }
      setVolume(0);
    }
  };

  const togglePlayback = () => {
    if (!audioRef.current) return;

    if (isPlaying) {
      audioRef.current.pause();
    } else {
      audioRef.current.play();
    }
    setIsPlaying(!isPlaying);
  };

  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, "0")}`;
  };

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    if (!recordingName.trim()) {
      alert("Please enter a recording name");
      return;
    }

    const file = event.target.files?.[0];
    if (file) {
      await processAudioFile(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = async (event: React.DragEvent) => {
    event.preventDefault();
    setIsDragging(false);

    if (!recordingName.trim()) {
      alert("Please enter a recording name");
      return;
    }

    const file = event.dataTransfer.files[0];
    if (file && file.type.startsWith("audio/")) {
      await processAudioFile(file);
    }
  };

  return (
    <div className="space-y-8">
      <div className="w-full">
        <label
          htmlFor="recordingName"
          className="block text-sm font-medium text-gray-700 mb-2"
        >
          Recording Name
        </label>
        <input
          type="text"
          id="recordingName"
          value={recordingName}
          onChange={(e) => setRecordingName(e.target.value)}
          placeholder="Enter recording name"
          className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-gray-800 placeholder-gray-400"
          disabled={isRecording || isProcessing}
        />
      </div>

      {isRecording && (
        <div className="space-y-4">
          <div className="w-full h-2 bg-gray-200 rounded-full overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-indigo-500 to-blue-500 transition-all duration-100"
              style={{ width: `${volume * 100}%` }}
            />
          </div>
          <div className="text-4xl font-mono text-center font-bold text-gray-800">
            {formatDuration(recordingDuration)}
          </div>
        </div>
      )}

      <div className="flex items-center justify-center gap-8">
        <button
          onClick={isRecording ? stopRecording : startRecording}
          className={`relative w-24 h-24 rounded-full flex items-center justify-center transition-all transform hover:scale-105 ${
            isRecording
              ? "bg-red-500 hover:bg-red-600 recording-ring"
              : "bg-gradient-to-r from-indigo-500 to-blue-500 hover:from-indigo-600 hover:to-blue-600"
          } shadow-lg disabled:opacity-50 disabled:cursor-not-allowed`}
          disabled={!recordingName.trim() || isProcessing}
        >
          {isRecording ? (
            <Square className="w-8 h-8 text-white" />
          ) : (
            <Mic className="w-8 h-8 text-white" />
          )}
        </button>

        <div
          className={`relative group cursor-pointer w-24 h-24 rounded-full flex items-center justify-center transition-all ${
            isDragging
              ? "bg-indigo-100 border-2 border-indigo-500 border-dashed"
              : "bg-gray-100 hover:bg-gray-200"
          }`}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <Upload className="w-8 h-8 text-gray-500 group-hover:text-gray-700" />
          <input
            ref={fileInputRef}
            type="file"
            accept="audio/*"
            onChange={handleFileUpload}
            className="hidden"
            disabled={isProcessing}
          />
          <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap">
            Upload audio file
          </div>
        </div>
      </div>

      {audioURL && (
        <div className="w-full space-y-4">
          <div className="flex items-center justify-center space-x-4">
            <button
              onClick={togglePlayback}
              className={`p-4 rounded-xl transition-all ${
                isPlaying
                  ? "bg-indigo-500 text-white shadow-lg shadow-indigo-500/30"
                  : "bg-gray-100 text-gray-700 hover:bg-gray-200"
              }`}
              disabled={isProcessing}
            >
              {isPlaying ? (
                <Pause className="w-6 h-6" />
              ) : (
                <Play className="w-6 h-6" />
              )}
            </button>
          </div>
          <audio
            ref={audioRef}
            src={audioURL}
            onEnded={() => setIsPlaying(false)}
            className="hidden"
          />
        </div>
      )}

      {isProcessing && (
        <div className="text-center">
          <div className="inline-flex items-center px-4 py-2 rounded-xl bg-indigo-50 text-indigo-600">
            <div className="animate-spin mr-2 h-4 w-4 border-2 border-indigo-600 border-t-transparent rounded-full" />
            Processing recording...
          </div>
        </div>
      )}
    </div>
  );
};

export default AudioRecorder;