import React from 'react';
import { Mic, Square, Upload } from 'lucide-react';

interface AudioControlsProps {
  isRecording: boolean;
  recordingName: string;
  onStartRecording: () => void;
  onStopRecording: () => void;
  onFileUpload: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onDragOver: (event: React.DragEvent) => void;
  onDragLeave: (event: React.DragEvent) => void;
  onDrop: (event: React.DragEvent) => void;
  isDragging: boolean;
  fileInputRef: React.RefObject<HTMLInputElement>;
}

const AudioControls: React.FC<AudioControlsProps> = ({
  isRecording,
  recordingName,
  onStartRecording,
  onStopRecording,
  onFileUpload,
  onDragOver,
  onDragLeave,
  onDrop,
  isDragging,
  fileInputRef,
}) => {
  return (
    <div className="flex flex-col md:flex-row items-center justify-center gap-8">
      <button
        onClick={isRecording ? onStopRecording : onStartRecording}
        className={`relative w-32 h-32 rounded-full flex items-center justify-center transition-all transform hover:scale-105 ${
          isRecording 
            ? 'bg-red-500 hover:bg-red-600 recording-ring' 
            : 'bg-gradient-to-r from-indigo-600 to-blue-500 hover:from-indigo-700 hover:to-blue-600'
        } shadow-xl disabled:opacity-50 disabled:cursor-not-allowed`}
        disabled={!recordingName.trim()}
      >
        {isRecording ? (
          <Square className="w-12 h-12 text-white" />
        ) : (
          <Mic className="w-12 h-12 text-white" />
        )}
      </button>

      <div 
        className={`relative group cursor-pointer w-32 h-32 rounded-full flex items-center justify-center transition-all ${
          isDragging 
            ? 'bg-indigo-100 border-2 border-indigo-500 border-dashed' 
            : 'bg-white hover:bg-gray-50 border border-gray-200'
        } shadow-lg`}
        onDragOver={onDragOver}
        onDragLeave={onDragLeave}
        onDrop={onDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <Upload className="w-10 h-10 text-gray-400 group-hover:text-indigo-600 transition-colors" />
        <input
          ref={fileInputRef}
          type="file"
          accept="audio/*"
          onChange={onFileUpload}
          className="hidden"
        />
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-4 px-4 py-2 bg-gray-800 text-white text-sm rounded-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap">
          Upload audio file
        </div>
      </div>
    </div>
  );
};

export default AudioControls;