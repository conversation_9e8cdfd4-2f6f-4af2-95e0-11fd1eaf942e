#!/usr/bin/env python3
# ============================================================================
# CACHE STORAGE ANALYZER
# ============================================================================
"""
Analyze cache storage usage and memory footprint.
"""

import os
import json
import pickle
from pathlib import Path
from data_manager import DataManager

def analyze_cache_storage():
    """Analyze cache storage usage in detail."""
    
    print("💾 Cache Storage Analysis")
    print("=" * 50)
    
    dm = DataManager()
    cache_dir = Path(dm.cache_dir)
    
    if not cache_dir.exists():
        print("❌ No cache directory found")
        return
    
    print(f"📁 Cache Location: {cache_dir.absolute()}")
    
    # Analyze each cache file
    cache_files = [
        ("text_elements.json", "PDF text content"),
        ("image_elements.json", "Image metadata and descriptions"),
        ("text_summaries.json", "Generated text summaries"),
        ("image_summaries.json", "Generated image summaries"),
        ("categorized_elements.json", "All categorized elements"),
        ("docstore.pkl", "LangChain document store (binary)"),
        ("doc_ids.json", "Document IDs for text"),
        ("image_ids.json", "Document IDs for images"),
        ("metadata.json", "Cache validation metadata")
    ]
    
    total_size = 0
    file_details = []
    
    print(f"\n📊 File-by-File Analysis:")
    print("-" * 70)
    print(f"{'File':<30} {'Size':<10} {'Type':<8} {'Description'}")
    print("-" * 70)
    
    for filename, description in cache_files:
        filepath = cache_dir / filename
        
        if filepath.exists():
            size_bytes = filepath.stat().st_size
            size_mb = size_bytes / (1024 * 1024)
            total_size += size_bytes
            
            # Determine file type
            file_type = "JSON" if filename.endswith('.json') else "Binary"
            
            print(f"{filename:<30} {size_mb:>6.2f} MB {file_type:<8} {description}")
            
            file_details.append({
                'name': filename,
                'size_bytes': size_bytes,
                'size_mb': size_mb,
                'type': file_type,
                'description': description
            })
        else:
            print(f"{filename:<30} {'Missing':<10} {'N/A':<8} {description}")
    
    print("-" * 70)
    total_mb = total_size / (1024 * 1024)
    print(f"{'TOTAL CACHE SIZE':<30} {total_mb:>6.2f} MB")
    
    # Memory usage analysis
    print(f"\n🧠 Memory Usage Analysis:")
    print("-" * 40)
    
    # Estimate RAM usage when loading cache
    estimated_ram = total_mb * 1.5  # JSON parsing overhead
    print(f"Estimated RAM during load: ~{estimated_ram:.1f} MB")
    
    # Additional vector store memory
    vector_store_ram = 50  # Estimated Chroma memory usage
    print(f"Vector store (Chroma): ~{vector_store_ram} MB")
    
    total_ram = estimated_ram + vector_store_ram
    print(f"Total RAM usage: ~{total_ram:.1f} MB")
    
    # Storage efficiency
    print(f"\n⚡ Storage Efficiency:")
    print("-" * 30)
    
    if file_details:
        largest_file = max(file_details, key=lambda x: x['size_mb'])
        smallest_file = min(file_details, key=lambda x: x['size_mb'])
        
        print(f"Largest file: {largest_file['name']} ({largest_file['size_mb']:.2f} MB)")
        print(f"Smallest file: {smallest_file['name']} ({smallest_file['size_mb']:.2f} MB)")
        
        json_files = [f for f in file_details if f['type'] == 'JSON']
        binary_files = [f for f in file_details if f['type'] == 'Binary']
        
        json_size = sum(f['size_mb'] for f in json_files)
        binary_size = sum(f['size_mb'] for f in binary_files)
        
        print(f"JSON files total: {json_size:.2f} MB ({len(json_files)} files)")
        print(f"Binary files total: {binary_size:.2f} MB ({len(binary_files)} files)")
    
    # Performance implications
    print(f"\n🚀 Performance Implications:")
    print("-" * 35)
    
    if total_mb < 10:
        performance = "Excellent"
        load_time = "~2-5 seconds"
    elif total_mb < 50:
        performance = "Good"
        load_time = "~5-10 seconds"
    elif total_mb < 100:
        performance = "Fair"
        load_time = "~10-15 seconds"
    else:
        performance = "Slow"
        load_time = "~15+ seconds"
    
    print(f"Cache size category: {performance}")
    print(f"Expected load time: {load_time}")
    print(f"Disk I/O: {'Light' if total_mb < 50 else 'Moderate' if total_mb < 100 else 'Heavy'}")
    
    # Recommendations
    print(f"\n💡 Recommendations:")
    print("-" * 25)
    
    if total_mb > 100:
        print("⚠️  Large cache size detected")
        print("   • Consider clearing cache periodically")
        print("   • Monitor disk space usage")
    elif total_mb < 5:
        print("✅ Optimal cache size")
        print("   • Fast loading expected")
        print("   • Minimal disk usage")
    else:
        print("✅ Good cache size")
        print("   • Reasonable loading time")
        print("   • Acceptable disk usage")
    
    # Storage location details
    print(f"\n📍 Storage Details:")
    print("-" * 25)
    print(f"Cache directory: {cache_dir.absolute()}")
    print(f"Storage type: Local disk (persistent)")
    print(f"Format: JSON (human-readable) + Pickle (binary)")
    print(f"Persistence: Survives system restarts")
    print(f"Portability: Cache tied to specific file paths")

def analyze_memory_during_operation():
    """Show memory usage during different operations."""
    
    print(f"\n🔄 Memory Usage During Operations:")
    print("=" * 45)
    
    operations = [
        {
            "operation": "Cache Loading",
            "ram_usage": "100-400 MB",
            "duration": "5-10 seconds",
            "description": "Loading all cached data into RAM"
        },
        {
            "operation": "Vector Store Setup", 
            "ram_usage": "50-200 MB",
            "duration": "2-5 seconds",
            "description": "Chroma database initialization"
        },
        {
            "operation": "Query Processing",
            "ram_usage": "10-50 MB",
            "duration": "2-5 seconds",
            "description": "Per-query embedding and retrieval"
        },
        {
            "operation": "Cache Saving",
            "ram_usage": "200-500 MB",
            "duration": "10-30 seconds", 
            "description": "Serializing and writing cache files"
        }
    ]
    
    print(f"{'Operation':<20} {'RAM Usage':<15} {'Duration':<15} {'Description'}")
    print("-" * 80)
    
    for op in operations:
        print(f"{op['operation']:<20} {op['ram_usage']:<15} {op['duration']:<15} {op['description']}")
    
    print(f"\n💡 Memory Management:")
    print("   • Python garbage collection handles cleanup")
    print("   • Peak usage during cache save/load operations")
    print("   • Steady-state usage ~100-200 MB during queries")

def main():
    """Run cache storage analysis."""
    
    try:
        analyze_cache_storage()
        analyze_memory_during_operation()
        
        print(f"\n🎯 Summary:")
        print("=" * 20)
        print("✅ Cache stored on disk (persistent)")
        print("✅ Reasonable memory usage (~100-400 MB)")
        print("✅ Fast loading from disk cache")
        print("✅ Efficient JSON + Binary storage")
        
    except Exception as e:
        print(f"💥 Analysis error: {e}")

if __name__ == "__main__":
    main()
