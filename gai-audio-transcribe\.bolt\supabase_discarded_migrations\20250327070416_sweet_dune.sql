/*
  # Create or update recordings table

  1. Changes
    - Create recordings table if it doesn't exist
    - Add user_id column with auth.users reference
    - Set up RLS policies for authenticated users

  2. Security
    - Enable RLS on recordings table
    - Add policies for authenticated users to:
      - Read their own recordings
      - Create new recordings
      - Update their own recordings
*/

-- First drop any existing policies to avoid conflicts
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Users can read own recordings" ON recordings;
  DROP POLICY IF EXISTS "Users can create recordings" ON recordings;
  DROP POLICY IF EXISTS "Users can update own recordings" ON recordings;
EXCEPTION
  WHEN undefined_object THEN
    NULL;
END $$;

-- Create table if it doesn't exist
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'recordings') THEN
    CREATE TABLE recordings (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      user_id uuid REFERENCES auth.users NOT NULL,
      name text NOT NULL,
      audio_url text NOT NULL,
      transcription text,
      tone text,
      rating numeric,
      status text NOT NULL DEFAULT 'processing',
      created_at timestamptz DEFAULT now()
    );
  END IF;
END $$;

-- Ensure RLS is enabled
ALTER TABLE recordings ENABLE ROW LEVEL SECURITY;

-- Create new policies
CREATE POLICY "Users can read own recordings"
  ON recordings
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create recordings"
  ON recordings
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own recordings"
  ON recordings
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);