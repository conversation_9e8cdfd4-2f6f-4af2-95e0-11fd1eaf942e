# Multimodal RAG System

A Retrieval-Augmented Generation (RAG) system that supports both text and image modalities.


## Setup

1. Create a virtual environment:
   ```
   python -m venv .venv
   ```

2. Activate the virtual environment:
   ```
   # On Windows
   .venv\Scripts\activate
   
   # On macOS/Linux
   source .venv/bin/activate
   ```

3. Install dependencies:
   ```
   pip install -r requirements.txt

   ```


## Useful Links
1. https://freedium.cfd/https://medium.com/data-science/multimodal-rag-process-any-file-type-with-ai-e6921342c903

2. https://github.com/langchain-ai/langchain/blob/master/cookbook/Semi_structured_and_multi_modal_RAG.ipynb?ref=blog.langchain.dev


