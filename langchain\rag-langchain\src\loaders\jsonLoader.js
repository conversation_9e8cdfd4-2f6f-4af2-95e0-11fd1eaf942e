import fs from "fs";
import { Document } from "langchain/document";

/**
 * @function customJsonLoader
 * @param {string} filePath
 * @description :: The function performs the following steps:
 * 1. take json file as input and will return a document array
 * 2. create document for each key value pair
 * 3. with this function we can create document to store in vector store and pass full context to llm
 * @returns :: { status: boolean, data: Document[]}
 */
export const customJsonLoader = async (filePath) => {
    try {
      const fileContent = fs.readFileSync(filePath, "utf-8");
      const jsonData = JSON.parse(fileContent);
      const docs = Object.entries(jsonData).map(([key, value]) => {
        return new Document({
          pageContent: JSON.stringify({ [key]: value }),
          metadata: { key, source: "./resume.json" },
        });
      });
  
      return {
        status: true,
        data: docs,
      };
    } catch (error) {
      console.log("jsonLoader :: customJsonLoader :: error :: ", error?.message);
      return {
        status: false,
        errorMsg: error?.message,
      };
    }
  };