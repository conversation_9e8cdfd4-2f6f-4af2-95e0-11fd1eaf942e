import { supabase } from '../lib/supabase';
import type { Recording, CallEvaluation, Rating } from '../types/recording';

export interface RecordingResponse {
  id: string;
  name: string;
  audio_url: string;
  transcription: string | null;
  tone: string | null;
  rating: number | null;
  status: 'processing' | 'completed' | 'error';
  created_at?: string;
  summary?: string | null;
  call_evaluation?: CallEvaluation;
}

export const saveRecording = async (recording: Omit<Recording, 'id' | 'created_at' | 'audio_url'> & { audioBlob: Blob }) => {
  try {
    const fileName = `${Date.now()}-${recording.name}.wav`;
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('audio-recordings')
      .upload(fileName, recording.audioBlob);

    if (uploadError) throw uploadError;

    const { data: { publicUrl } } = supabase.storage
      .from('audio-recordings')
      .getPublicUrl(fileName);

    const { data, error: dbError } = await supabase
      .from('recordings')
      .insert({
        name: recording.name,
        audio_url: publicUrl,
        status: recording.status,
        transcription: recording.transcription,
        tone: recording.tone,
        rating: recording.rating,
        summary: recording.summary,
        call_evaluation: recording.call_evaluation
      })
      .select()
      .single();

    if (dbError) throw dbError;
    return data;
  } catch (error) {
    console.error('Error saving recording:', error);
    throw error;
  }
};

export const fetchRecordings = async (): Promise<RecordingResponse[]> => {
  try {
    const { data, error } = await supabase
      .from('recordings')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching recordings:', error);
    throw error;
  }
};

export const simulateProcessing = async (recording: Recording): Promise<Recording> => {
  return new Promise((resolve) => {
    setTimeout(async () => {
      const success = Math.random() > 0.1;
      
      try {
        if (success) {
          const mockEvaluation: CallEvaluation = {
            call_greetings: { score: 4, comment: "Good greeting with proper introduction" },
            company_name: { score: 5, comment: "Company name clearly mentioned" },
            calling_reason: { score: 4, comment: "Purpose of call explained well" },
            soft_skills: { score: 18, comment: "Excellent politeness and patience shown" },
            service_time: { score: 8, comment: "Clear timeline provided" },
            customer_questions_handled: { score: 9, comment: "All questions addressed thoroughly" },
            language_proficiency: { score: 9, comment: "Clear and fluent communication" },
            clarity_of_communication: { score: 18, comment: "Very clear and well-articulated" }
          };

          const { data, error } = await supabase
            .from('recordings')
            .update({
              transcription: "This is a simulated transcription of the recorded audio.",
              tone: "Professional",
              rating: 4.5,
              status: 'completed',
              summary: "This is a summary of the conversation between the customer and technician.",
              call_evaluation: mockEvaluation
            })
            .eq('id', recording.id)
            .select()
            .single();

          if (error) throw error;
          resolve(data);
        } else {
          const { data, error } = await supabase
            .from('recordings')
            .update({
              status: 'error'
            })
            .eq('id', recording.id)
            .select()
            .single();

          if (error) throw error;
          resolve(data);
        }
      } catch (error) {
        console.error('Error processing recording:', error);
        throw error;
      }
    }, 2000);
  });
};