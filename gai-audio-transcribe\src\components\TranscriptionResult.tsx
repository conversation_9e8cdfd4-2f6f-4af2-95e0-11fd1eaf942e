import React from 'react';
import { AlertCircle, Star, StarHalf } from 'lucide-react';

interface TranscriptionResultProps {
  transcription: string;
  tone: string;
  rating: number;
}

const TranscriptionResult: React.FC<TranscriptionResultProps> = ({
  transcription,
  tone,
  rating,
}) => {
  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<Star key={i} className="w-6 h-6 text-yellow-400 fill-current" />);
      } else if (i === fullStars && hasHalfStar) {
        stars.push(<StarHalf key={i} className="w-6 h-6 text-yellow-400 fill-current" />);
      } else {
        stars.push(<Star key={i} className="w-6 h-6 text-gray-300" />);
      }
    }
    return stars;
  };

  return (
    <div className="w-full mt-6 animate-fadeIn">
      <div className="bg-white rounded-xl p-8 shadow-lg border border-gray-100 space-y-6">
        <div className="flex flex-wrap gap-4">
          <div className="relative group">
            <span className="inline-flex items-center px-4 py-2 rounded-xl text-sm font-medium bg-gray-100 text-gray-700">
              Tone: {tone}
              <AlertCircle className="w-4 h-4 ml-2" />
            </span>
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-4 py-2 bg-gray-800 text-white text-sm rounded-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap">
              Analysis of the speaker's tone and emotion
            </div>
          </div>

          <div className="relative group">
            <div className="flex items-center space-x-1 bg-gray-100 px-4 py-2 rounded-xl">
              {renderStars(rating)}
            </div>
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-4 py-2 bg-gray-800 text-white text-sm rounded-xl opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap">
              Call quality rating based on clarity and content
            </div>
          </div>
        </div>

        <div>
          <h3 className="text-xl font-semibold text-gray-800 mb-4">Transcription</h3>
          <p className="text-gray-700 leading-relaxed text-lg">
            {transcription}
          </p>
        </div>
      </div>
    </div>
  );
};

export default TranscriptionResult;