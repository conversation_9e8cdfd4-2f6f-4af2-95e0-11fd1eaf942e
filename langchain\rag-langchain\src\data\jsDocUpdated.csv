Page,Active Menu Tab,Active sub-menu,Action,Tabs,sub-tabs (if any),Form fields,after performing actions -1,after performing actions -2,Interactive elements,after performing actions -3,after performing actions -4
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Basic,,Name your service (Text Input) - Max 20 characters,,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Basic,,"Service key (Text Input) - Only lowercase letters, underscores, and max 20 characters",,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Basic,,"Nature of service (Radio Buttons) - Options: ""Task based"" and ""Project based""",,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Basic,,Service description (Text Input) - Max 200 characters,,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Basic,,"Prefix for request (Text Input) - Min 3, max 4 characters",,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Basic,,Select custom field for prefix (Text Input) - Max 10 characters,,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Basic,,Include customer name in ID? (Checkbox) - Max 10 characters from the first word,,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Help,,,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Statuses,Drag & drop the statuses,,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Statuses,"delete/ add the statuses under following categories- ACTIVE, DONE, CLOSED (closed category can have only single status)",,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Enter custom fields JSON (Textarea/Input for JSON),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Column span (Default is 2 for rendering a grid) (Number Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Rename Specific Details Label (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Service category field (Dropdown Select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Select alternate number fields (Dropdown Select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Start Time Slot (Lower Limit) (Dropdown Select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,End Time Slot (Upper Limit) (Dropdown Select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Are custom fields dynamic? (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Configure status update lambda (Section Header),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Enable lambda for status update (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Open (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Label for Open (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Schedule (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Label for Schedule (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Visit (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Label for Visit (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Assign (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Label for Assign (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Done (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Label for Done (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Closed (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Fields,Label for Closed (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Service Provider,Allow requests to be assigned to service providers (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Service Provider,Possible service providers (Multi-select dropdown with tags),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Service Provider,Default service provider (Dropdown select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Service Provider,Allow provider to add requests (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Service Provider,Auto move service request status to (Whenever onfield task is added) (Dropdown select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Service Provider,Configure Subtask Status Movement Based on Service Request Status (Expandable section),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Service Provider,Re-use existing customer profiles while creation (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Service Provider,Hide consumer phone number from provider (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Service Provider,Hide consumer phone number from onfield users of provider (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Service Provider,Make custom fields non-editable from provider (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Proactive,Enable Proactive (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Proactive,Select source service type (Dropdown select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Proactive,"Facts (List of predefined data fields, displayed as tags)",,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Proactive,"Rules (Section for defining rules)- New Rule (Button),  Output (Dropdown select), Add Rule (Button)",,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Show cumulative total on selected statuses (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Label for cumulative total (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Select statuses (Multi-select dropdown),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Open - Select field for cumulative total (Dropdown select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Enable value standardization (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Customize ageing timeframes (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Input Ageing timeframe JSON (Textarea),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Measure stage transition time (Aging) (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Measure TAT (open to closure) (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Measure feedback (only applicable if feedback is on) (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Measure stage-wise count (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Measure location-wise heat map (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Measure subtask vs task count (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Measure stage-wise TAT (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Measure assignees per request (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Measure number of requests per assignee (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Reporting,Choose fields to be displayed on authority-wise requests (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Customer mobile not mandatory? (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Pin code mandatory while request creation? (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Request service date mandatory while request creation? (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Can customer rate the service? (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,(An SMS will be sent to the customer to rate the service),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Send Feedback WhatsApp Message (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Show external order ID instead of TMS request ID (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Customer feedback form fields (Textarea/Input for JSON),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Preview Rate form (Button),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Open field creator (Button),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Send feedback SMS when request is moved to (Dropdown select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Ratings field (Dropdown select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Can users subscribe? (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Applicable sub-task type (Multi-select dropdown),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Configure auto status movement based on subtask creation (Expandable section),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Enable subtask closure on service request closure (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Authorities (Make sure that these roles have access to this service request) (Multi-select dropdown),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Hide authority selection from specific details (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Restrict manual authority selection during creation (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Enable cross visibility of selected authorities (Button),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Technician (Checkbox) --> visible only if selected in authorities multi-select,,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Manager (Checkbox)  --> visible only if selected in authorities multi-select,,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Technician - Show only direct reportees (Checkbox)  --> visible only if selected in authorities multi-select,,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Manager - Show only direct reportees (Checkbox) --> visible only if selected in authorities multi-select,,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Technician Specific Fields (only Technician of the selected Project-based can edit these fields) (Multi-select dropdown)--> visible only if selected in authorities multi-select,,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Entities,Manager Specific Fields (only Manager of the selected Project-based can edit these fields) (Multi-select dropdown)--> visible only if selected in authorities multi-select,,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Ratings,Enable Ratings (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Ratings,"Authorities (Section Header)-Here, you can configure who will rate which authority (Informational Text)",,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Ratings,Technician (Expandable Section),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Ratings,Another Authority / Static User (Radio Button),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Ratings,Manager (Dropdown Select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Ratings,Select Template (Dropdown Select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Ratings,Manager (Expandable Section),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Ratings,Another Authority / Static User (Radio Button),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Ratings,Static User (Text Input),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Ratings,Select Template (Dropdown Select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Ratings,Admin (Expandable Section),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,GAI,Enable GAI ratings for subtasks(Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Pricing Master,Masters Rate Table (Table with Input Fields),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Pricing Master,"Determination Engine Mode (radio buttons) - Lowest, highest, aggregate",,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Billing,Enable Billing (Checkbox),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Billing,Billing Type (Dropdown Select),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Billing,Categorize specific fields for billing section (Text Input),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Billing,Who can lock service request for billing (Multi-select Dropdown),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Billing,Who can sync service request price (Multi-select Dropdown),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Billing,Who can send a Request for billing (Multi-select Dropdown),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Billing,Who will get notified for billing (Multi-select Dropdown),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Discounting,Enable Discounting (Checkbox),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Discounting,"When discount approval status changes, notify (Multi-select Dropdown)",,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Discounting,Informational Message (Static Text),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Discounting,Add a Rule (Button),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Discounting,Discount Rules Table (Table with Input Fields and Actions-(edit & delete) ),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Discounting,"Pagination Controls (Pagination Buttons: Previous, Next)",,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Additional,Enable Additional Billing (Checkbox),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Additional,Columns (Button),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Billing,Additional,Preview Table (Table with Input Fields),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Fields,Automation Movement fields (Dropdown),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Authorities,Automation Movement Authorities (Dropdown),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Readiness,Select subtask (multi-select),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,,Select mandatory field for Task (multi-select),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Assignment,any role (eg.Technician)- auto assign based on location (Checkbox),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Assignment,Apply to unassigned requests (Button),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Assignment,Apply to all active requests (Button),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Periodic,Add automation (Button) ,Add a periodic automation (Section Header),,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Periodic,Add automation (Button) ,Title (Text Input - Required),,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Periodic,Add automation (Button) ,Cron frequency (Text Input - Required),,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Periodic,Add automation (Button) ,Statuses (Multi-select Dropdown - Required),,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Periodic,Add automation (Button) ,Lambda ARN (Text Input - Required),,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Periodic,Add automation (Button) ,Add (Button),,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Subtask,Configure Subtask Status Movement Based on Service Request Status (Expandable Section),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Subtask,Enable? (Checkbox),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Subtask,Service Type Statuses (Multi-select Dropdown),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Subtask,Subtask Status Mapping Table (Table with Dropdown Inputs),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Automations,Subtask,"Pagination Controls (Pagination Buttons: Previous, Next)",,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Tabular view,Select table columns (Multi-select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Configuration,Views,Select Brand Filters (Multi-select),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Status wise,Consumer,select one of the status (Expandable section),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Status wise,Consumer,SMS / WhatsApp (Tab Selection),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Status wise,Consumer,Enable (Checkbox),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Status wise,Consumer,Status Type (Dropdown Select),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Status wise,Consumer,Template (Dropdown Select),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Status wise,Authorities,"When updated as ""any status from config"" notify (multi-select)",,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Field wise,Authority (tab),Add a Notification (button),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Field wise,Authority (tab),Select specific field (Dropdown Select),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Field wise,Authority (tab),Select authorities (Dropdown Select),,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Field wise,Authority (tab),Notification added will be visible in tabular format,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Event based,Service Request Deletion (Expandable Section),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Event based,Enable Lambda Hook (Checkbox),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Event based,Lambda ARN (Text Input - Required),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Time based,Add a Notification (button),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Time based,Add a time-based notification rule (Section Header),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Time based,Notification Title (Text Input - Required),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Time based,Notification Start Date (Date Picker - Required),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Time based,Repeat Every (Number Input - Required),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Time based,Repeat Interval (Dropdown Select - Required),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Time based,"Options: Day, Week, Month, etc.",,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Time based,Notification will trigger at 9:00 AM (Informational Text),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Time based,Select Primary Recipient (Text Input - Required),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Time based,Static CC Users (Multi-select Dropdown),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Time based,Select status for ageing report notification (Dropdown Select - Required),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Time based,Lambda ARN for ageing report notification (Text Input - Required),,,,,
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Notification,Time based,Notification added will be visible in tabular format with edit/delete functionality,,,,,
/setup/srvc-req/sub-tasks-types,Service Configuration,Service types,select any 1 Subtask type,Basic,,"Name your subtask type (Text Input - Required, Max 20 characters)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Basic,,"Subtask key (Text Input - Required, Only lowercase letters, underscores, Max 20 characters)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Basic,,Onfield task? (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Basic,,"(If this task is an onfield task, then check the above box)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Basic,,"Subtask description (Textarea - Required, Max 200 characters)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Basic,,Icon (Dropdown Select - Required),,,,,
/setup/srvc-req/sub-tasks-types,,,,Statuses,,Drag & drop the statuses,,,,,
/setup/srvc-req/sub-tasks-types,,,,Statuses,,"delete/ add the statuses under following categories- ACTIVE, DONE, CLOSED (closed category can have only single status)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Assignee Based,,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Notify Subtask Assignees on Assignment/Reassignment? (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Notify due tasks for the day? (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Notify at (Dropdown Select - Required),,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Notify assignee on subtask deletion? (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Send reminder for upcoming job? (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Authority Based,,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,"When updated as ""any status from config"" notify (Multi-select Dropdown)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Statuswise ,,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Lambda ,,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,"Open..""statuses of the config"" (Expandable Section)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Enable Lambda Hook (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Lambda ARN (Text Input - Required),,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Event based,,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Subtask Deletion (Expandable Section),,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Enable Lambda Hook (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Lambda ARN (Text Input - Required),,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Subtask Re-assignment (Expandable Section),,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Enable Lambda Hook (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Notification,,Lambda ARN (Text Input - Required),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,Disallow multiple assignments at the same time? (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,Disallow multiple subtask creations on a request? (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,Disallow subtask status update before the start date? (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,"Disable default assignee filtration by ""reporting to""? (Checkbox)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,Show assignees by service location? (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,Configure Consumer OTP verification (Expandable Section),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,Configure Mandatory Statuses,,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,"""Each status of config "" (Dropdown)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,Configure Geo Fencing,,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,Geo verification limit in meters (Number Input),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,Geo verification mode (Dropdown Select),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,Enable Geo verification for (Multi-select Dropdown),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,Configure service request status movement based on subtask status (Expandable Section),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,Attendance status (Select the status that will mark the start of attendance for the task) (Dropdown Select),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Basic,Set attendance marking buffer (in minutes) (Number Input),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Assigned,Lambda for auto assign (Text Input - Placeholder: Enter Lambda ARN),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Assigned,Tabs Section (Informational Text),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Assigned,By Service Types (Tab Selection),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Assigned,Select Service Types (Multi-select Dropdown),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Assigned,Enable service type filtration for Task based (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Assigned,Enable unassigned filter for Task based (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Assigned,Enable present filter for Task based (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Assigned,Enable location group matching for Task based (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Assigned,Enable role filter for Task based (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Assigned,Info Box (Informational Text),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Assigned,Enable nearby filter for Task based (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Assigned,Max range for nearby filtration in meters (Number Input),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Restrictions ,Disallow status re-updation (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Restrictions ,Select statuses to be allowed for multiple updation (Multi-select Dropdown),,,,,
/setup/srvc-req/sub-tasks-types,,,,Automations,Restrictions ,Hide statuses for updation (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Status wise fields,,"""status from config"" Fields (Section Header)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Status wise fields,,"Edit ""status from config""  Fields (Link/Button)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Status wise fields,,Move attachments field to bottom (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Status wise fields,,"Make attachments field mandatory for ""status from config""  (Checkbox)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Status wise fields,,Move remarks field to bottom (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Status wise fields,,Enable lambda-based validation (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Status wise fields,,"Is ""status from config""  status form a dynamic form? (Checkbox)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Status wise fields,,"Column span of ""status from config""  form fields (Default is 4 for rendering a grid) (Number Input)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Status wise fields,,Show audio transcripts (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Status wise fields,,Select fields to transcribe (Multi-select Dropdown - Required),,,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Configure Subtask Card (Expandable Section),Show call consumer button on the subtask (Checkbox),,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Configure Subtask Card (Expandable Section),Enable calling through dialer (Checkbox),,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Configure Subtask Card (Expandable Section),Enable masked calling through dialer (Checkbox),,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Configure Subtask Card (Expandable Section),Enable alternate number calling (if present in service request) (Checkbox),,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Configure Subtask Card (Expandable Section),Select fields to be hidden on the subtask card (Multi-select Dropdown),,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Configure Subtask Card (Expandable Section),Show external order id instead of TMS request ID (Checkbox),,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Dashboard Configuration (Expandable Section),Select statuses to be hidden on the dashboard (only statuses in the Done bucket will be shown) (Multi-select Dropdown),,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Dashboard Configuration (Expandable Section),Hide subtask on service request closure (Checkbox),,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Lock subtask details for selected statuses (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Can postpone? (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,"When postponing, what fields to capture (Textarea - JSON configuration)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Form preview (Button),,,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Edit postpone fields (Link),,,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Can reject? (Checkbox),,,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,"When rejecting, what fields to capture (Textarea - JSON configuration)",,,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Form preview (Button),,,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Edit reject fields (Link),,,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,Start Time Slot (Lower Limit) (Time Picker),,,,,
/setup/srvc-req/sub-tasks-types,,,,Exceptions,,End Time Slot (Upper Limit) (Time Picker),,,,,
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),"Select Color (Color Picker)
",
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),"Name (Text Input, Required)",
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Description (Multiline Text Area),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),"Role Code (Text Input, Required)",
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Features Access (Multi-select Tags),Manage Feature Rights (Checkbox Group)
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Features Access (Multi-select Tags),READ (Checkbox)
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Features Access (Multi-select Tags),CREATE (Checkbox)
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Features Access (Multi-select Tags),UPDATE (Checkbox)
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Service Access (Multi-select Tags),Manage Service Rights (Checkbox Group)
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Service Access (Multi-select Tags),READ (Checkbox)
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Service Access (Multi-select Tags),CREATE (Checkbox)
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Service Access (Multi-select Tags),UPDATE (Checkbox)
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Service Access (Multi-select Tags),DELETE (Checkbox)
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Define sequence of subtask type listing on the dashboard (Text Input),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Onfield App access (Multi-select Dropdown),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Can add leaves for below roles (Multi-select Dropdown),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Enable auto punch out (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Can see ratings of below roles (Multi-select Dropdown),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Can see consumer phone number (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Enable call consumer button (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Enable live tracking (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Configure hidden fields (Expandable Section),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Hide due tasks widget on dashboard (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Hide TAT overview widget on dashboard (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Hide customer feedback widget on dashboard (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Move aging widget on top in dashboard (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Show assigned service requests only (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Show authorized service requests only (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Is Admin (Radio Button),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Is On field (Radio Button),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Is eligible for inbound billing (Radio Button),
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),Submit (Button),
/setup/user-config/roles,User configuration,Roles,,,,,,,ALL FILTERS (Button),,
/setup/user-config/roles,User configuration,Roles,,,,,,,Search bar (text input),gives results as per requested ,
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),"Select Color (Color Picker)
",
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),"Name (Text Input, Required)",
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Description (Multiline Text Area),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),"Role Code (Text Input, Required)",
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Features Access (Multi-select Tags),Manage Feature Rights (Checkbox Group)
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),,READ (Checkbox)
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),,CREATE (Checkbox)
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),,UPDATE (Checkbox)
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Service Access (Multi-select Tags),Manage Service Rights (Checkbox Group)
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),,READ (Checkbox)
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),,CREATE (Checkbox)
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),,UPDATE (Checkbox)
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),,DELETE (Checkbox)
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Define sequence of subtask type listing on the dashboard (Text Input),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Onfield App access (Multi-select Dropdown),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Can add leaves for below roles (Multi-select Dropdown),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Enable auto punch out (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Can see ratings of below roles (Multi-select Dropdown),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Can see consumer phone number (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Enable call consumer button (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Enable live tracking (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Configure hidden fields (Expandable Section),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Hide due tasks widget on dashboard (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Hide TAT overview widget on dashboard (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Hide customer feedback widget on dashboard (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Move aging widget on top in dashboard (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Show assigned service requests only (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Show authorized service requests only (Checkbox),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Is Admin (Radio Button),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Is On field (Radio Button),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Is eligible for inbound billing (Radio Button),
/setup/user-config/roles,User configuration,Roles,,,,,,,roles created as list (click any one),Save (Button),
/setup/user-config/roles,User configuration,Roles,,,,,,,Pagination Controls (Button),,
/setup/user-config/roles,,Locations,,,,,,,Add location group (Button),"Group name (Required, Text Input)",
/setup/user-config/roles,,Locations,,,,,,,Add location group (Button),Pincode (Textarea),
/setup/user-config/roles,,Locations,,,,,,,Add location group (Button),Cities (Autocomplete Text Input),
/setup/user-config/roles,,Locations,,,,,,,Add location group (Button),State (Autocomplete Text Input),
/setup/user-config/roles,,Locations,,,,,,,Add location group (Button),Exclude cities (Autocomplete Text Input),
/setup/user-config/roles,,Locations,,,,,,,Add location group (Button),Description (Textarea),
/setup/user-config/roles,,Locations,,,,,,,Add location group (Button),Submit (Button),
/setup/user-config/roles,,Locations,,,,,,,Search bar (text input),,
/setup/user-config/roles,,Locations,,,,,,,roles created as list (click any one),"Group name (Required, Text Input)",
/setup/user-config/roles,,Locations,,,,,,,roles created as list (click any one),Pincode (Textarea),
/setup/user-config/roles,,Locations,,,,,,,roles created as list (click any one),Cities (Autocomplete Text Input),
/setup/user-config/roles,,Locations,,,,,,,roles created as list (click any one),State (Autocomplete Text Input),
/setup/user-config/roles,,Locations,,,,,,,roles created as list (click any one),Exclude cities (Autocomplete Text Input),
/setup/user-config/roles,,Locations,,,,,,,roles created as list (click any one),Description (Textarea),
/setup/user-config/roles,,Locations,,,,,,,roles created as list (click any one),Save (Button),
/setup/user-config/roles,,Locations,,,,,,,Pagination Controls (Button),,
/setup/user-config/roles,,Zones,,,,,,,Add Zone (Button),"Zone name (Required, Text Input)",
/setup/user-config/roles,,Zones,,,,,,,Add Zone (Button),"Select Location Group (Required, Dropdown Select)",
/setup/user-config/roles,,Zones,,,,,,,Add Zone (Button),Description (Textarea),
/setup/user-config/roles,,Zones,,,,,,,Add Zone (Button),Submit (Button),
/setup/user-config/roles,,Zones,,,,,,,Search bar (text input),,
/setup/user-config/roles,,Zones,,,,,,,roles created as list (click any one),"Zone name (Required, Text Input)",
/setup/user-config/roles,,Zones,,,,,,,roles created as list (click any one),"Select Location Group (Required, Dropdown Select)",
/setup/user-config/roles,,Zones,,,,,,,roles created as list (click any one),Description (Textarea),
/setup/user-config/roles,,Zones,,,,,,,roles created as list (click any one),Save (Button),
/setup/user-config/roles,,Zones,,,,,,,Pagination Controls (Button),,
/setup/user-config/roles,,User Fields,,,,User custom fields (Textarea),,,,,
/setup/user-config/roles,,User Fields,,,,Open field creator (Button),,,,,
/setup/user-config/roles,,User Fields,,,,Submit (Button),,,,,
/setup/user-config/roles,,Restrictions ,,,,Max simultaneous logins (value between 1 to 4) (Number Input),,,,,
/setup/user-config/roles,,Restrictions ,,,,Restrict user from changing password (Checkbox),,,,,
/setup/user-config/roles,,Restrictions ,,,,Enable password policy (Checkbox),,,,,
/setup/user-config/roles,,Restrictions ,,,,Submit (Button),,,,,
/setup/inventory-config/manage-custom-fields,Inventory Config,Custom Fields,,,,SKUs custom fields (Textarea - JSON input),,,,,
/setup/inventory-config/manage-custom-fields,Inventory Config,Custom Fields,,,,Open field creator (Link/Button),,,,,
/setup/inventory-config/manage-custom-fields,Inventory Config,Custom Fields,,,,Spare custom fields (Textarea - JSON input),,,,,
/setup/inventory-config/manage-custom-fields,Inventory Config,Custom Fields,,,,Open field creator (Link/Button),,,,,
/setup/inventory-config/manage-custom-fields,Inventory Config,Custom Fields,,,,Warehouse custom fields (Textarea - JSON input),,,,,
/setup/inventory-config/manage-custom-fields,Inventory Config,Custom Fields,,,,Form preview (Button),,,,,
/setup/inventory-config/manage-custom-fields,Inventory Config,Custom Fields,,,,Open field creator (Link/Button),,,,,
/setup/inventory-config/manage-custom-fields,Inventory Config,Custom Fields,,,,Product Stock Custom Fields (Textarea - JSON input),,,,,
/setup/inventory-config/manage-custom-fields,Inventory Config,Custom Fields,,,,Open field creator (Link/Button),,,,,
/setup/inventory-config/manage-custom-fields,Inventory Config,Custom Fields,,,,Spare Stock Custom Fields (Textarea - JSON input),,,,,
/setup/inventory-config/manage-custom-fields,Inventory Config,Custom Fields,,,,Open field creator (Link/Button),,,,,
/setup/inventory-config/manage-custom-fields,Inventory Config,Custom Fields,,,,Submit (Button),,,,,
/rating/rating-templates,Rating Templates ,,,,,,,,Add a template ,Template Name (Text Input - Required),
/rating/rating-templates,Rating Templates ,,,,,,,,,Enter custom fields JSON (Textarea - Required),
/rating/rating-templates,Rating Templates ,,,,,,,,,Open field creator (Link/Button),
/rating/rating-templates,Rating Templates ,,,,,,,,,Select rating field (Dropdown),
/rating/rating-templates,Rating Templates ,,,,,,,,,Create a template (Button),
/rating/rating-templates,Rating Templates ,,,,,,,,List of templates added (click any one),Active/inactive (toggle-Button),
/rating/rating-templates,Rating Templates ,,,,,,,,List of templates added (click any one),Edit (Button),Template Name (Text Input - Required)
/rating/rating-templates,Rating Templates ,,,,,,,,List of templates added (click any one),Edit (Button),Enter custom fields JSON (Textarea - Required)
/rating/rating-templates,Rating Templates ,,,,,,,,List of templates added (click any one),Edit (Button),Open field creator (Link/Button)
/rating/rating-templates,Rating Templates ,,,,,,,,List of templates added (click any one),Edit (Button),Select rating field (Dropdown)
/rating/rating-templates,Rating Templates ,,,,,,,,List of templates added (click any one),Edit (Button),Update template (Button)
/setup/srvc-req/settings,Settings,Regional,,,,Select organization timezone (Dropdown),,,,,
/setup/srvc-req/settings,Settings,Regional,,,,Select consumer phone number country code (Dropdown),,,,,
/setup/srvc-req/settings,Settings,Regional,,,,Select number of digits for the phone number (Number Input - Required),,,,,
/setup/srvc-req/settings,Settings,Regional,,,,Select organization country code for pincode (Dropdown),,,,,
/setup/srvc-req/settings,Settings,Regional,,,,Organization pincode length (Number Input - Read-Only),,,,,
/setup/srvc-req/settings,Settings,Regional,,,,Submit (Button),,,,,
/setup/srvc-req/settings,Settings,Communications,,Lambda,,Enter lambda ARN for voice calling number (Text Input),,,,,
/setup/srvc-req/settings,Settings,Communications,,Lambda,,Enter lambda ARN for WhatsApp communication (Text Input),,,,,
/setup/srvc-req/settings,Settings,Communications,,Lambda,,Enter lambda ARN for SMS communication (Text Input),,,,,
/setup/srvc-req/settings,Settings,Communications,,Lambda,,Submit (Button),,,,,
/setup/srvc-req/settings,Settings,Communications,,SMS template,Add SMS Template,Status Type ( Dropdown - Single select),,,,,
/setup/srvc-req/settings,Settings,Communications,,SMS template,Consumer OTP Template ,* Add new template value for Consumer OTP (Text Input),,,,,
/setup/srvc-req/settings,Settings,Communications,,SMS template,,* Add new template label for Consumer OTP (Text Input),,,,,
/setup/srvc-req/settings,Settings,Communications,,SMS template,SMS Feedback Template,Template Name (Text Input),,,,,
/setup/srvc-req/settings,Settings,Communications,,SMS template,SMS Feedback Template,* Add new template value for Consumer Feedback (Text Input),,,,,
/setup/srvc-req/settings,Settings,Communications,,SMS template,SMS Feedback Template,* Add new template label for Consumer Feedback (Text Input),,,,,
/setup/srvc-req/settings,Settings,Communications,,WhatsApp template ,Status Wise Template,Status Type ( Dropdown - Single select),,,,,
/setup/srvc-req/settings,Settings,Communications,,WhatsApp template ,Status Wise Template,apply (Button),,,,,
/setup/srvc-req/settings,Settings,Communications,,WhatsApp template ,Feedback Template,Template Name (Text Input),,,,,
/setup/srvc-req/settings,Settings,Communications,,WhatsApp template ,Feedback Template,* Add new template value for Consumer Feedback (Text Input),,,,,
/setup/srvc-req/settings,Settings,Communications,,WhatsApp template ,Feedback Template,* Add new template label for Consumer Feedback (Text Input),,,,,
/setup/srvc-req/settings,Settings,Communications,,WhatsApp template ,Feedback Template,Add new template token for Consumer Feedback (Text Input),,,,,
/setup/srvc-req/settings,Settings,Communications,,WhatsApp template ,Feedback Template,Add new template url for Consumer Feedback (Text Input),,,,,
/users,,,,,,,,,Create User (Button),Click here for Bulk creation (Expandable section),to be discussed
/users,,,,,,,,,Create User (Button),* Full Name (Text Input),
/users,,,,,,,,,Create User (Button),Code (Text Input),
/users,,,,,,,,,Create User (Button),Designation (Text Input),
/users,,,,,,,,,Create User (Button),* Select Specific Role (Dropdown Select),
/users,,,,,,,,,Create User (Button),Select Location Group (Dropdown Select),
/users,,,,,,,,,Create User (Button),Select Reporting To (Dropdown Select),
/users,,,,,,,,,Create User (Button),* Mobile Number (+91) (Text Input),
/users,,,,,,,,,Create User (Button),* Email (Text Input),
/users,,,,,,,,,Create User (Button),Service Types (Multi-select or Dropdown Select),
/users,,,,,,,,,Create User (Button),Address Details (Collapsible Section),Search your building/street name (Search Input with Icon Button - Text Input)
/users,,,,,,,,,Create User (Button),Address Details (Collapsible Section),Pick on Map (Button)
/users,,,,,,,,,Create User (Button),Address Details (Collapsible Section),Flat no (Text Input)
/users,,,,,,,,,Create User (Button),Address Details (Collapsible Section),Building/Apartment name (Text Input)
/users,,,,,,,,,Create User (Button),Address Details (Collapsible Section),Line 1 (Text Input - Address Line 1)
/users,,,,,,,,,Create User (Button),Address Details (Collapsible Section),Line 2 (Text Input - Address Line 2)
/users,,,,,,,,,Create User (Button),Address Details (Collapsible Section),Pincode (Text Input - Numeric)
/users,,,,,,,,,Create User (Button),Address Details (Collapsible Section),City (Text Input)
/users,,,,,,,,,Create User (Button),Address Details (Collapsible Section),State (Text Input)
/users,,,,,,,,,Create User (Button),Address Details (Collapsible Section),Latitude (Disabled Text Input - Example: Eg 37.7749)
/users,,,,,,,,,Create User (Button),Address Details (Collapsible Section),Longitude (Disabled Text Input - Example: Eg -122.4194)
/users,,,,,,,,,Create User (Button),* Password (Password Input - Hidden Characters),
/users,,,,,,,,,Create User (Button),* Confirm Password (Password Input - Hidden Characters),
/users,,,,,,,,,Create User (Button),Active (Toggle Switch),
/users,,,,,,,,,Create User (Button),Submit (Button),
/users,,,,,,,,,Bulk Update (Button),Download File (Button),
/users,,,,,,,,,,Upload File (Button),
/users,,,,,,,,,ALL FILTERS (Button/Link),,
/users,,,,,,,,,Created users list (click on any one),Full Name (Text Input - Required),
/users,,,,,,,,,Created users list (click on any one),Code (Text Input - Unique identifier for the user),
/users,,,,,,,,,Created users list (click on any one),Designation (Text Input - Optional),
/users,,,,,,,,,Created users list (click on any one),Select Specific Role (Dropdown with multiple selections - Required),
/users,,,,,,,,,Created users list (click on any one),Select Location Group (Dropdown - Optional),
/users,,,,,,,,,Created users list (click on any one),Select Reporting To (Dropdown - Optional),
/users,,,,,,,,,Created users list (click on any one),Mobile Number (+91) (Text Input - Required),
/users,,,,,,,,,Created users list (click on any one),Email (Text Input),
/users,,,,,,,,,Created users list (click on any one),Service Types (Text Input/Dropdown - Optional),
/users,,,,,,,,,Created users list (click on any one),Address Details (expandable section),Search your building/street name (Text Input)
/users,,,,,,,,,Created users list (click on any one),Address Details (expandable section),Pick on Map (Button)
/users,,,,,,,,,Created users list (click on any one),Address Details (expandable section),Flat No (Text Input )
/users,,,,,,,,,Created users list (click on any one),Address Details (expandable section),Building/Apartment Name (Text Input )
/users,,,,,,,,,Created users list (click on any one),Address Details (expandable section),Line 1 (Text Input )
/users,,,,,,,,,Created users list (click on any one),Address Details (expandable section),Line 2 (Text Input )
/users,,,,,,,,,Created users list (click on any one),Address Details (expandable section),Pincode (Text Input )
/users,,,,,,,,,Created users list (click on any one),Address Details (expandable section),City (Text Input )
/users,,,,,,,,,Created users list (click on any one),Address Details (expandable section),State (Text Input )
/users,,,,,,,,,Created users list (click on any one),Address Details (expandable section),Latitude (Text Input)
/users,,,,,,,,,Created users list (click on any one),Address Details (expandable section),Longitude (Text Input)
/users,,,,,,,,,Created users list (click on any one),Change Password (Button),
/users,,,,,,,,,Created users list (click on any one),Active (Toggle Switch),
/users,,,,,,,,,Created users list (click on any one),Save (Button),
/customer,,,,,,,,,Create Customer,Click here for Bulk creation (Expandable section),to be discussed
/customer,,,,,,,,,Create Customer,Full Name - Text (Required),
/customer,,,,,,,,,Create Customer,Code - Text (Required),
/customer,,,,,,,,,Create Customer,Email - Email (Required),
/customer,,,,,,,,,Create Customer,Mobile Number (+91) - Number (Required),
/customer,,,,,,,,,Create Customer,Search your building/street name - Text with Search,
/customer,,,,,,,,,Create Customer,Flat No - Text,
/customer,,,,,,,,,Create Customer,Building/Apartment Name - Text,
/customer,,,,,,,,,Create Customer,Line 1 - Text,
/customer,,,,,,,,,Create Customer,Line 2 - Text,
/customer,,,,,,,,,Create Customer,Pincode - Dropdown/Number,
/customer,,,,,,,,,Create Customer,City - Dropdown/Text,
/customer,,,,,,,,,Create Customer,State - Dropdown,
/customer,,,,,,,,,Create Customer,Submit - Button,
/customer,,,,,,,,,Search bar(Text),,
/customer,,,,,,,,,created customers list (click anyone),Details tab,Full Name - Text (Required)
/customer,,,,,,,,,created customers list (click anyone),Details tab,Code - Text (Required)
/customer,,,,,,,,,created customers list (click anyone),Details tab,Email - Email (Required)
/customer,,,,,,,,,created customers list (click anyone),Details tab,Mobile Number (+91) - Number (Required)
/customer,,,,,,,,,created customers list (click anyone),Details tab,Search your building/street name - Text with Search
/customer,,,,,,,,,created customers list (click anyone),Details tab,Flat No - Text
/customer,,,,,,,,,created customers list (click anyone),Details tab,Building/Apartment Name - Text
/customer,,,,,,,,,created customers list (click anyone),Details tab,Line 1 - Text
/customer,,,,,,,,,created customers list (click anyone),Details tab,Line 2 - Text
/customer,,,,,,,,,created customers list (click anyone),Details tab,Pincode - Dropdown/Number
/customer,,,,,,,,,created customers list (click anyone),Details tab,City - Dropdown/Text
/customer,,,,,,,,,created customers list (click anyone),Details tab,State - Dropdown
/customer,,,,,,,,,created customers list (click anyone),Details tab,Save - Button
/customer,,,,,,,,,created customers list (click anyone),History tab,