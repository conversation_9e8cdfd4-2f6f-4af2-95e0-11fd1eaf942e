import dotenv from "dotenv";
import { CSVLoader } from "@langchain/community/document_loaders/fs/csv";
import { JSONLoader } from "langchain/document_loaders/fs/json";
import { PDFLoader } from "@langchain/community/document_loaders/fs/pdf";
import {
  getDataSplit,
  getVectorStoreAndRetriever,
} from "./services/vectorStore.js";
import { getChain, getLlm, getPromptTemplate } from "./services/llm.js";
import { customCsvLoader } from "./loaders/csvLoader.js";

dotenv.config();

/**
 * @function textGenerateByRag
 * @description Processes a document using RAG (Retrieval-Augmented Generation) to generate responses
 * @description The function performs the following steps:
 * 1. Loads and processes a CSV/JSON/PDF file
 * 2. Splits the document into chunks
 * 3. Creates a vector store for similarity search
 * 4. Sets up the prompt template
 * 5. Initializes the language model
 * 6. Creates a RAG chain
 * 7. Performs similarity search and generates response
 * @returns {Promise<string|null>} The generated response from the LLM, or null if an error occurs
 * @throws {Error} When any of the processing steps fail
 */

const textGenerateByRag = async () => {
  try {
    // Get the file path

    const filePath = "./data/jsDocUpdated.csv";
    //const filePath = "./resume.json";
    //const filePath = "./frankenstein.pdf";
    //const loader = new CSVLoader(filePath);
    //const loader = new PDFLoader(filePath, { parsedItemSeparator: "" });
    //const docs = await loader.load();

    //const { data: docs, status } = await customJsonLoader(filePath);
    const { data: docs, status } = await customCsvLoader(filePath);

    if (!status) {
      return {
        status: false,
        message: `status :: ${docs || "something went wrong"} `,
      };
    }

    const {
      status: splitStatus,
      data,
      errorMsg: splitErrorMsg,
    } = await getDataSplit({ data: docs });

    if (!splitStatus) {
      return {
        status: false,
        message: `splitStatus :: ${splitErrorMsg || "something went wrong"} `,
      };
    }

    const {
      data: vectorStoreAndRetrieverData,
      status: vectorStatus,
      errorMsg: vectorErrorMsg,
    } = await getVectorStoreAndRetriever({
      splits: data?.splits,
    });
    if (!vectorStatus) {
      return {
        status: false,
        message: `vectorStatus :: ${vectorErrorMsg || "something went wrong"} `,
      };
    }

    const {
      data: promptTemplateData,
      status: promptTemplateStatus,
      errorMsg: promptTemplateErrorMsg,
    } = await getPromptTemplate();
    if (!promptTemplateStatus) {
      return {
        status: false,
        message: `promptTemplateStatus :: ${
          promptTemplateErrorMsg || "something went wrong"
        } `,
      };
    }

    const {
      data: llmData,
      status: llmStatus,
      errorMsg: llmErrorMsg,
    } = await getLlm();
    if (!llmStatus) {
      return {
        status: false,
        message: `llmStatus :: ${llmErrorMsg || "something went wrong"} `,
      };
    }

    const {
      data: ragChainData,
      status: chainStatus,
      errorMsg: chainErrorMsg,
    } = await getChain({
      promptTemplate: promptTemplateData.promptTemplate,
      llm: llmData.llm,
    });
    if (!chainStatus) {
      return {
        status: false,
        message: `chainStatus :: ${chainErrorMsg || "something went wrong"} `,
      };
    }

    //PDF
    //const query = `who is frankenstein`;
    //const query = `what was the wish of the creature of frankenstein?`;
    //const query = `what is the theme of the book?`;
    //const query = `where was the first murder committed and who was accused of murder?`;

    //CSV
    //const query = `what does facts mean in proactive tab`;
    //const query = `List all form fields of Entities tab of service type in points.`;
    //const query = `Analyze the sheet. tell me steps to be taken to update the service type's service type description.`;
    //const query = `how to enable GAI rating for subtask?`;
    //const query = `what is proactive and how to use it. tell me all form fields of it.`;
    //const query = `list all after performing actions -1 of billing sub tab of configuration tab.`;
    //const query = `tell me what all actions can i perform on billing tab`;
    const query = `list all the form fields of automation sub tab in configuration tab.`;

    //JSON
    //const query = `Is there any internship or training mentioned if yes give the company details`;
    //const query = `list down all the project with their tech stack`;
    //const query = `tell me personal details of the candidate`;
    const retrievedDocs =
      await vectorStoreAndRetrieverData.vectorStore.similaritySearch(query);

    const _res = await ragChainData.ragChain.invoke({
      question: query,
      context: retrievedDocs,
    });
    console.log("indexjs :: textGenerateByRag :: _res :: ", _res);

    return _res;
  } catch (error) {
    console.log("indexjs :: textGenerateByRag ::  error :: ", error);
  }
};

await textGenerateByRag();
