import React from 'react';

interface RecordingNameInputProps {
  value: string;
  onChange: (value: string) => void;
  disabled: boolean;
}

const RecordingNameInput: React.FC<RecordingNameInputProps> = ({
  value,
  onChange,
  disabled,
}) => {
  return (
    <div className="w-full">
      <label htmlFor="recordingName" className="block text-lg font-medium text-gray-700 mb-3">
        Recording Name
      </label>
      <input
        type="text"
        id="recordingName"
        value={value}
        onChange={(e) => onChange(e.target.value)}
        placeholder="Enter recording name"
        className="w-full px-6 py-4 bg-white rounded-xl border border-gray-200 focus:ring-2 focus:ring-indigo-500 focus:border-transparent text-gray-800 placeholder-gray-400 text-lg shadow-sm transition-all"
        disabled={disabled}
      />
    </div>
  );
};

export default RecordingNameInput;