from typing import Any
from pydantic import BaseModel
import fitz  # PyMuPDF
import pdfplumber
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
import uuid
from langchain.retrievers.multi_vector import MultiVectorRetriever
from langchain.storage import InMemoryStore
from langchain_chroma import Chroma
from langchain_core.documents import Document
from langchain_openai import OpenAIEmbeddings
from langchain_core.runnables import RunnablePassthrough
import os
from dotenv import load_dotenv
import openai
import base64 # Required for image encoding if you were to pass raw bytes to a multimodal LLM later
from langsmith import traceable
import shutil # Import shutil for directory operations

#explore pillow for images

# Get absolute path to the PDF file in 'data' folder relative to your script
base_dir = os.path.dirname(os.path.abspath(__file__))  # directory of your running script
path = os.path.join(base_dir, "..", "data", "visual_tuning.pdf")  # ../data/visual_tuning.pdf

# --- Create a directory to save extracted images ---
image_output_dir = os.path.join(base_dir, "extracted_images")
os.makedirs(image_output_dir, exist_ok=True) # Create the directory if it doesn't exist

# --- Extract text and images with PyMuPDF ---
doc = fitz.open(path)
text_elements = []
image_elements = [] # List to store paths to extracted images


for page_num in range(len(doc)):
    page = doc.load_page(page_num)
    text = page.get_text("text")
    if text:
        text_elements.append(text)

    # Extract images
    images = page.get_images(full=True)
    for img_index, img in enumerate(images):
        xref = img[0]
        base_image = doc.extract_image(xref)
        image_bytes = base_image["image"]
        image_ext = base_image["ext"]
        # Generate a unique filename for each image
        image_filename = f"page_{page_num}_img_{img_index}_{uuid.uuid4().hex}.{image_ext}"
        image_path = os.path.join(image_output_dir, image_filename)

        # Save the image bytes to a file
        try:
            with open(image_path, "wb") as f:
                f.write(image_bytes)
            image_elements.append(image_path) # Store the path to the saved image
            print(f"Saved image: {image_path}")
        except Exception as e:
            print(f"Error saving image {image_filename}: {e}")


# --- Extract tables with pdfplumber ---
table_elements = []
with pdfplumber.open(path) as pdf:
    for page in pdf.pages:
        tables = page.extract_tables()
        print("tables==>",tables)
        for table in tables:
            # Each table is a list of rows (each row is a list of cells)
            table_elements.append(table)

# --- Count categories ---
category_counts = {
    "table": len(table_elements),
    "text": len(text_elements),
    "image": len(image_elements) # Include image count
}
print("Category counts:", category_counts)

# --- Define Element model ---
class Element(BaseModel):
    type: str
    text: Any
    image_path: str | None = None # Added for image elements

# --- Categorize elements ---
categorized_elements = []
for table in table_elements:
    # Convert table to string for LangChain prompt (simple join or json dump)
    table_str = "\n".join([", ".join([str(cell) if cell else "" for cell in row]) for row in table])
    categorized_elements.append(Element(type="table", text=table_str))

for text in text_elements:
    categorized_elements.append(Element(type="text", text=text))


client = openai.OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))

# Add image elements to the categorized list
def encode_image_to_base64(image_path):
    with open(image_path, "rb") as f:
        return base64.b64encode(f.read()).decode("utf-8")
    
for image_path in image_elements:
    try:
        print(f"Processing image: {image_path}")
        ext = image_path.split('.')[-1]  # e.g., 'jpeg' or 'png'
        image_base64 = encode_image_to_base64(image_path)

        response = client.chat.completions.create(
            model="gpt-4o",
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": "You are an assistant tasked with summarizing image. \
Give a concise summary of the provided image."},
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/{ext};base64,{image_base64}"
                            },
                        },
                    ],
                }
            ],
            max_tokens=300,
        )
        summary = response.choices[0].message.content
        print("summary===>",summary)
        categorized_elements.append(Element(type="image", text=summary, image_path=image_path))

    except Exception as e:
        print(f"Error processing image {image_path}: {e}")
    


# --- Separate lists by type ---
table_elements = [e for e in categorized_elements if e.type == "table"]
text_elements = [e for e in categorized_elements if e.type == "text"]
image_elements = [e for e in categorized_elements if e.type == "image"] # New list for images

print("Tables count:", len(table_elements))
print("Text count:", len(text_elements))
print("Images count:", len(image_elements))


# --- Prompt for summary ---
# The prompt for summarization is still text-based, even for images, as we're summarizing to text.
prompt_text = """You are an assistant tasked with summarizing tables, text, and descriptions of images. \
Give a concise summary of the provided content. Content chunk: {element} """
prompt = ChatPromptTemplate.from_template(prompt_text)

# --- Setup summarization chain ---
# Using gpt-4 for summarization. Note: If you wanted truly *visual* summaries,
# you'd need a multimodal model here (e.g., gpt-4o) and different input format.
model = ChatOpenAI(temperature=0, model="gpt-4o",api_key=os.environ.get("OPENAI_API_KEY"))
summarize_chain = {"element": lambda x: x} | prompt | model | StrOutputParser()

# --- Summarize texts, tables, and images ---
text_summaries = summarize_chain.batch([e.text for e in text_elements], {"max_concurrency": 5})
table_summaries = summarize_chain.batch([e.text for e in table_elements], {"max_concurrency": 5})
# For images, we summarize the placeholder text. For better results, you'd feed actual image descriptions from a Vision LLM here.
# image_summaries = summarize_chain.batch([e.text for e in image_elements], {"max_concurrency": 5})
image_summaries = [e.text for e in image_elements]


# --- Setup vectorstore and retriever ---

# The vectorstore to use to index the child chunks
vectorstore = Chroma(collection_name="summaries", embedding_function=OpenAIEmbeddings(api_key=os.environ.get("OPENAI_API_KEY")))

# The storage layer for the parent documents
store = InMemoryStore()
id_key = "doc_id" # key to use to establish connection between summaries (vector store) and original content (doc store)

# The retriever (empty to start)
retriever = MultiVectorRetriever(
    vectorstore=vectorstore,
    docstore=store,
    id_key=id_key,
)

# --- Add summarized texts ---
doc_ids = [str(uuid.uuid4()) for _ in text_elements]
summary_texts_docs = [
    Document(page_content=s, metadata={id_key: doc_ids[i], "type": "text_summary"})
    for i, s in enumerate(text_summaries)
]
retriever.vectorstore.add_documents(summary_texts_docs)
# Store original text content
retriever.docstore.mset(list(zip(doc_ids, [Document(page_content=e.text, metadata={"type": "original_text"}) for e in text_elements])))


# --- Add summarized tables ---
table_ids = [str(uuid.uuid4()) for _ in table_elements]
summary_tables_docs = [
    Document(page_content=s, metadata={id_key: table_ids[i], "type": "table_summary"})
    for i, s in enumerate(table_summaries)
]
retriever.vectorstore.add_documents(summary_tables_docs)
# Store original table content
retriever.docstore.mset(list(zip(table_ids, [Document(page_content=e.text, metadata={"type": "original_table"}) for e in table_elements])))


# --- Add summarized images ---
image_ids = [str(uuid.uuid4()) for _ in image_elements]
summary_images_docs = [
    Document(page_content=s, metadata={id_key: image_ids[i], "type": "image_summary"})
    for i, s in enumerate(image_summaries)
]

retriever.vectorstore.add_documents(summary_images_docs)
# Store original image paths in the docstore
# The MultiVectorRetriever will retrieve these Document objects.
# The page_content is the image_path.
retriever.docstore.mset(list(zip(image_ids, [Document(page_content=e.image_path, metadata={"type": "original_image_path"}) for e in image_elements])))


# --- Final RAG prompt ---
# This template needs to be flexible enough for multimodal context.
# However, since the retriever only returns text content (original text/table strings and image paths),
# the final LLM will still process text.
template = """Answer the question based only on the following context, which can include text, tables, and references to images.
If an image is relevant, mention its filename or a descriptive reference.

Context:
{context}

Question: {question}
"""
prompt = ChatPromptTemplate.from_template(template)

# --- RAG pipeline ---
# The model is still ChatOpenAI with gpt-4, which is text-only.
# To make it truly Option 3, this model would need to be gpt-4o or a similar multimodal LLM
# and the 'context' would need to be formatted as a list of content parts including image_url.
model = ChatOpenAI(temperature=0, model="gpt-4o", api_key=os.environ.get("OPENAI_API_KEY"))

# Custom formatting for context to explicitly show image paths
def _format_context(docs):
    formatted_docs = []
    for doc in docs:
        if doc.metadata.get("type") == "original_image_path":
            print("doc.page_content==>",doc)

            # For image paths, just list the path or a reference
            formatted_docs.append(f"Image Reference: {os.path.basename(doc.page_content)}")
        else:
            formatted_docs.append(doc.page_content)
    return "\n\n".join(formatted_docs)


chain = (
    {"context": retriever | _format_context, "question": RunnablePassthrough()}
    | prompt
    | model
    | StrOutputParser()
)

# # --- Invoke with your question ---
# table - Can you compare the different models listed in the table in terms of their context handling capabilities?
#         Can you summarize the differences between the models shown in the table in terms of input modalities?
# image - Which image involves a question about yogurt flavor? tell me what is shown exactly in there
#         Is there any image in the dataset where the actions seem unsafe or unconventional?
answer = chain.invoke(
    "Which image involves a question about yogurt flavor? tell me what is shown exactly in there"
)

print("ANSWER =====>",answer)



## --- Deleting the Entire Directory ---


# --- Cleanup: Delete the entire extracted_images directory ---
try:
    if os.path.exists(image_output_dir): # Check if the directory exists before attempting to delete
        shutil.rmtree(image_output_dir)
        print(f"Deleted directory: {image_output_dir}")
except OSError as e:
    print(f"Error deleting directory {image_output_dir}: {e}")

