// import { openai, openaiModels } from "./libs/openai";

const { default: axios } = require("axios");
const { OpenAI } = require("openai");
// Import the Supabase client
const { createClient } = require("@supabase/supabase-js");
const { type } = require("os");
require("dotenv").config();
const DATA = require("./data").data;

// Initialize Supabase
// const supabaseUrl = "https://fpeddabgvcaapsfjuwee.supabase.co"; // Replace with your Supabase project URL
// const supabaseKey = process.env.SUPABASE_KEY;
//  ; // Replace with your Supabase service role key or anon key
// const supabase = createClient(supabaseUrl, supabaseKey);

const openai = new OpenAI({
  apiKey:
    process.env.OPENAI_API_KEY,
});

const tools = [
  {
    type: "function",
    name: "get_supabase_rec",
    description: "Get data from the supabase",
    parameters: {
      type: "object",
      properties: {
        status: {
          type: "string",
          description: "status of a recording e.g error , completed",
        },
        rating: {
          type: "number",
          description: "rating of a recording e.g 4",
        },
        operator: {
          type: "string",
          description: "operator to be used in the query e.g gt,lt,eq",
        },
      },
    },
  },
];

const input = [
  {
    role: "user",
    content: `
  get me all recording names where rating is grater than 90.5
 `,
  },
];

const get_supabase_rec = async (args) => {
  try {
    const { operator, rating } = args;

    // Validate the operator to prevent invalid queries
    if (!['gt', 'lt', 'eq', 'gte', 'lte'].includes(operator)) {
      throw new Error("Invalid operator. Supported operators: gt, lt, eq, gte, lte.");
    }


    const data = DATA.filter((item) => {
      switch (operator) {
        case "gt": // Greater than
          return item.rating > rating;
        case "lt": // Less than
          return item.rating < rating;
        case "eq": // Equal to
          return item.rating === rating;
        case "gte": // Greater than or equal to
          return item.rating >= rating;
        case "lte": // Less than or equal to
          return item.rating <= rating;
        default:
          return false; // Return false for unsupported operators
      }
    });

    // Dynamically build the query based on the operator
    // const { data, error } = await supabase
    //   .from("recordings") // Replace with your actual table name
    //   .select("*")
    //   .filter("rating", operator, rating);  // Use the operator to filter by 'rating'

    // if (error) {
    //   throw new Error(error.message);
    // }

    // Return the data if the query was successful
    return data;
  } catch (error) {
    console.error("Error fetching data:", error.message);
    throw error;  // Rethrow the error for further handling if needed
  }
};

const askAi = async () => {
  const response = await openai.responses.create({
    model: "gpt-4o",
    input,
    tools,
  });

  console.log("resp", response.output);

  return response.output[0];
};

const main = async () => {
  const toolCall = await askAi();

  const arguments = JSON.parse(toolCall.arguments);

  console.log("toolCall", toolCall);

  const dataFromDb = await get_supabase_rec(arguments);
  //const finalResp = await askAi();

  input.push(toolCall); // append model's function call message
  input.push({
    // append result message
    type: "function_call_output",
    call_id: toolCall.call_id,
    output: JSON.stringify(dataFromDb),
  });

  const response2 = await openai.responses.create({
    model: "gpt-4o",
    input,
    tools,
    store: true,
  });
   console.log("dataFromDb", dataFromDb.length , response2.output_text);
};

main();
