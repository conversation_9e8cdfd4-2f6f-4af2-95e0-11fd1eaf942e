# ============================================================================
# OPTIMIZED MULTIMODAL RAG SYSTEM FOR VEHICLE ANALYSIS
# ============================================================================

from typing import Any, List, Dict
from pydantic import BaseModel
import fitz
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI
import uuid
from langchain.retrievers.multi_vector import MultiVectorRetriever
from langchain.storage import InMemoryStore
from langchain_chroma import Chroma
from langchain_core.documents import Document
from langchain_openai import OpenAIEmbeddings
from langchain_core.runnables import RunnablePassthrough
import os
from dotenv import load_dotenv
import openai
import base64
import shutil
import piexif
import google.generativeai as genai
import io
from PIL import Image
import json
import glob
import time
from langchain.retrievers import EnsembleRetriever
from langchain_community.retrievers import BM25Retriever
from data_manager import DataManager
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize data manager for caching (same as main_with_cache.py)
data_manager = DataManager(cache_dir="data_cache")

load_dotenv()

# ============================================================================
# CONFIGURATION
# ============================================================================

base_dir = os.path.dirname(os.path.abspath(__file__))
pdf_path = "./carBigData/cars_196_description.pdf"
image_output_dir = os.path.join(base_dir, "extracted_images_with_metadata")
os.makedirs(image_output_dir, exist_ok=True)

client = openai.OpenAI(api_key=os.environ.get("OPENAI_API_KEY"))
genai.configure(api_key=os.environ.get("GEMINI_API_KEY"))

# ============================================================================
# ENHANCED ELEMENT MODEL
# ============================================================================


class Element(BaseModel):
    """Enhanced element model with more metadata"""

    type: str
    text: Any
    image_path: str | None = None
    page_num: int | None = None  # Add page number for text elements
    source: str | None = None  # Add source information


# ============================================================================
# UTILITY FUNCTIONS
# ============================================================================


def encode_image_to_base64(image_path):
    with open(image_path, "rb") as f:
        return base64.b64encode(f.read()).decode("utf-8")


def read_image_metadata(image_path: str) -> str:
    img = Image.open(image_path)
    exif_data = img.info.get("exif")

    if not exif_data:
        return None

    try:
        exif_dict = piexif.load(exif_data)
        desc = (
            exif_dict["0th"]
            .get(piexif.ImageIFD.ImageDescription, b"")
            .decode("utf-8", errors="ignore")
        )
        return desc if desc else None
    except Exception:
        return None


def save_image_with_metadata(image_bytes: bytes, save_path: str, description: str):
    img = Image.open(io.BytesIO(image_bytes)).convert("RGB")
    exif_dict = {"0th": {}, "Exif": {}, "GPS": {}, "1st": {}, "thumbnail": None}
    exif_dict["0th"][piexif.ImageIFD.ImageDescription] = description.encode(
        "utf-8", errors="ignore"
    )
    exif_bytes = piexif.dump(exif_dict)
    img.save(save_path, "JPEG", exif=exif_bytes)


# ============================================================================
# ENHANCED TEXT EXTRACTION WITH CHUNKING
# ============================================================================


# def extract_text_with_chunking(pdf_path: str, chunk_size: int = 1200) -> List[Element]:
#     """Extract text from PDF with intelligent chunking"""
#     doc = fitz.open(pdf_path)
#     text_elements = []

#     for page_num in range(len(doc)):
#         page = doc.load_page(page_num)
#         text = page.get_text("text")

#         if text:
#             # Split text into paragraphs or sections
#             paragraphs = text.split("\n\n")
#             current_chunk = ""

#             for para in paragraphs:
#                 if len(current_chunk) + len(para) < chunk_size:
#                     current_chunk += para + "\n\n"
#                 else:
#                     if current_chunk:
#                         text_elements.append(
#                             Element(
#                                 type="text",
#                                 text=current_chunk.strip(),
#                                 page_num=page_num,
#                                 source=f"PDF page {page_num + 1}",
#                             )
#                         )
#                     current_chunk = para + "\n\n"

#             # Add remaining chunk
#             if current_chunk:
#                 text_elements.append(
#                     Element(
#                         type="text",
#                         text=current_chunk.strip(),
#                         page_num=page_num,
#                         source=f"PDF page {page_num + 1}",
#                     )
#                 )

#     doc.close()
#     return text_elements


# Vehicle-specific intelligent chunking (recommended for your use case)
def extract_vehicle_text_with_smart_chunking(
    pdf_path: str, chunk_size: int = 1200, overlap_size: int = 300
) -> List[Element]:
    """Extract vehicle text with intelligent chunking designed for vehicle descriptions"""
    doc = fitz.open(pdf_path)
    text_elements = []

    for page_num in range(len(doc)):
        page = doc.load_page(page_num)
        text = page.get_text("text")

        if text:
            # Try to identify vehicle sections (look for vehicle names at start of sections)
            vehicle_sections = identify_vehicle_sections(text)

            for vehicle_name, section_text in vehicle_sections:
                # For each vehicle, create chunks with overlap
                chunks = create_overlapping_chunks(
                    section_text, chunk_size, overlap_size
                )

                for i, chunk in enumerate(chunks):
                    if chunk.strip():
                        text_elements.append(
                            Element(
                                type="text",
                                text=chunk.strip(),
                                page_num=page_num,
                                source=f"PDF page {page_num + 1} - {vehicle_name}, chunk {i + 1}",
                            )
                        )

    doc.close()
    return text_elements


def create_overlapping_chunks(
    text: str, chunk_size: int, overlap_size: int
) -> List[str]:
    """Create overlapping text chunks"""
    if len(text) <= chunk_size:
        return [text]

    chunks = []
    start = 0

    while start < len(text):
        # Calculate end position
        end = start + chunk_size

        # If this is not the last chunk, try to break at word boundary
        if end < len(text):
            # Find the last space within the chunk to avoid breaking words
            last_space = text.rfind(" ", start, end)
            if last_space > start:
                end = last_space

        # Extract the chunk
        chunk = text[start:end]
        chunks.append(chunk)

        # If we've reached the end of text, break
        if end >= len(text):
            break

        # Move start position for next chunk (with overlap)
        start = end - overlap_size

        # Ensure we don't go backwards
        if start < 0:
            start = 0

    return chunks


def identify_vehicle_sections(text: str) -> List[tuple]:
    """Identify individual vehicle sections in the text"""
    # Look for patterns like "Vehicle Name (Year)" at the start of lines
    import re

    # Split by potential vehicle headers (lines that look like vehicle titles)
    sections = []
    lines = text.split("\n")
    current_section = ""
    current_vehicle = "Unknown Vehicle"

    for line in lines:
        # Check if line looks like a vehicle header (contains brand name and year)
        if re.match(r"^[A-Z][a-zA-Z\s-]+(.*\(\d{4}\)|.*\d{4}.*)", line.strip()):
            # Save previous section if it exists
            if current_section.strip():
                sections.append((current_vehicle, current_section.strip()))

            # Start new section
            current_vehicle = line.strip()
            current_section = line + "\n"
        else:
            current_section += line + "\n"

    # Add the last section
    if current_section.strip():
        sections.append((current_vehicle, current_section.strip()))

    return sections


# ============================================================================
# ENHANCED IMAGE PROCESSING
# ============================================================================


def process_images_with_metadata(
    image_folder_path: str, output_dir: str
) -> List[Element]:
    """Process images with enhanced metadata extraction"""
    os.makedirs(output_dir, exist_ok=True)

    supported_extensions = [".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".webp"]
    image_files = []
    for ext in supported_extensions:
        image_files.extend(glob.glob(os.path.join(image_folder_path, f"*{ext}")))
        # image_files.extend(glob.glob(os.path.join(image_folder_path, f"*{ext.upper()}")))

    image_elements = []

    prompt = """
    Analyze this vehicle image and provide a comprehensive description.
    Return a JSON object with these exact keys:
    {
        "vehicle_type": "<car, truck, motorcycle, etc.>",
        "make_model": "<brand and model if identifiable>",
        "color": "<primary color>",
        "condition": "<new, used, etc.>",
        "features": {
            "body_style": "...",
            "doors": "...",
            "special_features": "..."
        },
        "setting": "<location/context>",
        "detailed_description": "<comprehensive description for search>",
        "search_keywords": ["keyword1", "keyword2", ...]
    }
    """

    for image_file_path in image_files:
        try:
            print(f"Processing: {os.path.basename(image_file_path)}")

            with open(image_file_path, "rb") as f:
                image_bytes = f.read()

            ext = os.path.splitext(image_file_path)[1][1:]
            original_name = os.path.splitext(os.path.basename(image_file_path))[0]
            image_filename = f"{original_name}_{uuid.uuid4().hex}.{ext}"
            image_path = os.path.join(output_dir, image_filename)

            # Generate metadata using OpenAI
            img_base64 = base64.b64encode(image_bytes).decode("utf-8")

            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {"type": "text", "text": prompt},
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/{ext};base64,{img_base64}"
                                },
                            },
                        ],
                    }
                ],
            )

            metadata = response.choices[0].message.content
            save_image_with_metadata(image_bytes, image_path, metadata)

            # Create enhanced summary for better retrieval
            summary_prompt = f"""
            Based on this metadata, create a comprehensive searchable description that includes:
            - Vehicle identification (make, model, type)
            - Visual characteristics
            - Any unique features
            - Context and setting
            
            Metadata: {metadata}
            """

            summary_response = client.chat.completions.create(
                model="gpt-4o",
                messages=[{"role": "user", "content": summary_prompt}],
                max_tokens=300,
            )

            summary = summary_response.choices[0].message.content

            image_elements.append(
                Element(
                    type="image",
                    text=summary,
                    image_path=image_path,
                    source=f"Image: {original_name}",
                )
            )

            print(f"✅ Processed {image_filename}")
            time.sleep(3)  # Rate limiting

        except Exception as e:
            print(f"❌ Error processing {image_file_path}: {e}")
            continue

    return image_elements


# ============================================================================
# ENHANCED RETRIEVER SETUP
# ============================================================================


def create_enhanced_retriever(
    text_elements: List[Element], image_elements: List[Element]
):
    """Create an enhanced multi-vector retriever with better configuration"""

    # Initialize vector store with better configuration
    vectorstore = Chroma(
        collection_name="vehicle_summaries",
        embedding_function=OpenAIEmbeddings(
            api_key=os.environ.get("OPENAI_API_KEY"),
            model="text-embedding-3-small",  # Use newer embedding model
        ),
        persist_directory="./chroma_db_vehicles",
    )

    # Initialize document store
    store = InMemoryStore()
    id_key = "doc_id"

    # Create multi-vector retriever with proper k value
    retriever = MultiVectorRetriever(
        vectorstore=vectorstore,
        docstore=store,
        id_key=id_key,
        search_kwargs={"k": 50},  # Properly set k value
    )

    # Generate better summaries for text elements
    print("Generating text summaries...")
    model = ChatOpenAI(temperature=0, model="gpt-4o")

    text_summaries = []
    for element in text_elements:
        try:
            summary_prompt = f"""
            Create a comprehensive summary of this vehicle information that includes:
            - Vehicle names and models mentioned
            - Key specifications
            - Unique features
            - Use cases
            Make it searchable and information-rich.
            
            Content: {element.text[:2000]}  # Limit to avoid token issues
            """

            response = model.invoke(summary_prompt)
            text_summaries.append(response.content)
            print(f"✅ Successfully processed text element from {element.source}")

        except Exception as e:
            print(f"❌ Error processing text element from {element.source}: {str(e)}")
            # Add fallback summary to keep the process going
            text_summaries.append(
                f"Summary unavailable for {element.source}: {element.text[:200]}..."
            )

    # Add text documents
    doc_ids = [str(uuid.uuid4()) for _ in text_elements]
    summary_docs = [
        Document(
            page_content=summary,
            metadata={
                id_key: doc_ids[i],
                "type": "text_summary",
                "source": text_elements[i].source,
                "page": text_elements[i].page_num,
            },
        )
        for i, summary in enumerate(text_summaries)
    ]

    retriever.vectorstore.add_documents(summary_docs)
    retriever.docstore.mset(
        list(
            zip(
                doc_ids,
                [
                    Document(
                        page_content=e.text,
                        metadata={"type": "original_text", "source": e.source},
                    )
                    for e in text_elements
                ],
            )
        )
    )

    # Add image documents
    image_ids = [str(uuid.uuid4()) for _ in image_elements]
    image_summary_docs = [
        Document(
            page_content=e.text,
            metadata={
                id_key: image_ids[i],
                "type": "image_summary",
                "source": e.source,
            },
        )
        for i, e in enumerate(image_elements)
    ]

    retriever.vectorstore.add_documents(image_summary_docs)
    retriever.docstore.mset(
        list(
            zip(
                image_ids,
                [
                    Document(
                        page_content=e.image_path,
                        metadata={"type": "original_image_path", "source": e.source},
                    )
                    for e in image_elements
                ],
            )
        )
    )

    return retriever, vectorstore


# ============================================================================
# ENHANCED CONTEXT FORMATTING
# ============================================================================


def format_context_enhanced(docs: List[Document]) -> str:
    """Enhanced context formatting with better structure"""
    text_docs = []
    image_refs = []

    for doc in docs:
        if doc.metadata.get("type") == "original_image_path":
            # Read actual metadata from image
            metadata = read_image_metadata(doc.page_content)
            image_refs.append(
                f"Image: {os.path.basename(doc.page_content)}\nMetadata: {metadata}\n"
            )
        else:
            text_docs.append(doc.page_content)

    context = ""
    if text_docs:
        context += "TEXT INFORMATION:\n" + "\n---\n".join(text_docs)
    if image_refs:
        context += "\n\nIMAGE REFERENCES:\n" + "\n".join(image_refs)

    return context


# ============================================================================
# HYBRID RETRIEVER (OPTIONAL - For Better Results)
# ============================================================================


def create_hybrid_retriever(
    text_elements: List[Element], image_elements: List[Element]
):
    """Create a hybrid retriever combining dense and sparse retrieval"""

    # Create the standard multi-vector retriever
    mv_retriever, vectorstore = create_enhanced_retriever(text_elements, image_elements)

    # Create BM25 retriever for keyword matching
    all_texts = [e.text for e in text_elements] + [e.text for e in image_elements]
    bm25_retriever = BM25Retriever.from_texts(all_texts)
    bm25_retriever.k = 10

    # Combine retrievers with ensemble
    ensemble_retriever = EnsembleRetriever(
        retrievers=[mv_retriever, bm25_retriever],
        weights=[0.7, 0.3],  # Favor semantic search but include keyword matching
    )

    return ensemble_retriever, vectorstore


# ============================================================================
# MAIN PIPELINE
# ============================================================================


def main(output_file: str = "qa_data_optimized.json"):
    logger.info("🚀 Starting Multimodal RAG System with Caching")
    
    # Check if cache is valid (same logic as main_with_cache.py)
    if data_manager.is_cache_valid(pdf_path, "./carBigData/carImg"):
        logger.info("📦 Loading data from cache...")
        
        # Load from cache
        cached_data = data_manager.load_data()
        if cached_data:
            (
                text_elements_cache,
                image_elements,
                text_summaries,
                image_summaries,
                categorized_elements,
                docstore_data,
                doc_ids,
                image_ids,
            ) = cached_data
            
            # Convert cached text elements back to Element objects
            text_elements = []
            for i, text_content in enumerate(text_elements_cache):
                text_elements.append(Element(
                    type="text",
                    text=text_content,
                    page_num=0,  # Default value
                    source=f"Cached text element {i+1}"
                ))
            
            logger.info(f"✅ Loaded from cache: {len(text_elements)} text elements, {len(image_elements)} image elements")
            
            # Setup retriever with cached data
            retriever, vectorstore = setup_retriever_from_cache(
                text_elements, image_elements, text_summaries, image_summaries,
                docstore_data, doc_ids, image_ids
            )
            
        else:
            logger.error("❌ Failed to load from cache, will process from scratch")
            cached_data = None
    else:
        logger.info("🔄 Cache invalid or missing, processing from scratch...")
        cached_data = None
    
    # Process data if not loaded from cache
    if cached_data is None:
        # Extract text with better chunking
        print("Extracting text from PDF...")
        text_elements = extract_vehicle_text_with_smart_chunking(pdf_path)
        print(f"Extracted {len(text_elements)} text chunks")

        # Process images
        print("Processing images...")
        if len(os.listdir(image_output_dir)) == 0:
            image_elements = process_images_with_metadata(
                "./carBigData/carImg", image_output_dir
            )
        else:
            # Load existing processed images
            image_elements = []
            for filename in os.listdir(image_output_dir):
                if filename.lower().endswith(".jpg"):
                    img_path = os.path.join(image_output_dir, filename)
                    metadata = read_image_metadata(img_path)
                    if metadata:
                        image_elements.append(
                            Element(
                                type="image",
                                text=metadata,
                                image_path=img_path,
                                source=f"Image: {filename}",
                            )
                        )

        print(f"Processed {len(image_elements)} images")

        # Create enhanced retriever (this will generate summaries)
        print("Creating retriever...")
        retriever, vectorstore = create_enhanced_retriever(text_elements, image_elements)
        
        # Save to cache after processing
        logger.info("💾 Saving processed data to cache...")
        
        # Get the summaries and docstore data from the retriever
        text_summaries, image_summaries, doc_ids, image_ids = extract_summaries_from_retriever(retriever, text_elements, image_elements)
        
        # Convert text elements to strings for caching
        text_elements_for_cache = [e.text for e in text_elements]
        
        success = data_manager.save_data(
            text_elements=text_elements_for_cache,
            image_elements=image_elements,
            text_summaries=text_summaries,
            image_summaries=image_summaries,
            categorized_elements=text_elements + image_elements,  # Combined elements
            docstore=retriever.docstore,
            doc_ids=doc_ids,
            image_ids=image_ids,
            pdf_path=pdf_path,
            image_dir="./carBigData/carImg",
        )
        
        if success:
            logger.info("✅ Data saved to cache successfully")
        else:
            logger.warning("⚠️ Failed to save data to cache")
    
    # Display statistics
    print(f"\n📊 System Statistics:")
    print(f"Text elements: {len(text_elements)}")
    print(f"Image elements: {len(image_elements)}")
    
    # Display cache info
    cache_info = data_manager.get_cache_info()
    if cache_info.get("cache_exists"):
        print(f"Cache size: {cache_info.get('cache_size_mb', 0)} MB")
        print(f"Cache created: {cache_info.get('created_at', 'Unknown')}")

    # Create QA chain with better prompt
    template = """You are an expert on vehicles. Answer the question using ONLY the provided context.
    Be specific and detailed in your response. If images are mentioned, describe what they show  and give their file path.
    
    Context:
    {context}
    
    Question: {question}
    
    Answer (be specific and cite the information source when possible):"""

    prompt = ChatPromptTemplate.from_template(template)
    model = ChatOpenAI(temperature=0, model="gpt-4o")

    chain = (
        {
            "context": retriever | format_context_enhanced,
            "question": RunnablePassthrough(),
        }
        | prompt
        | model
        | StrOutputParser()
    )

    # Test queries
    test_queries = [
        "Show me a car from the dataset that belongs to Ferrari. Explain how its design reflects speed and aerodynamics."
    ]

    print("\n" + "=" * 80)
    print("TESTING RETRIEVAL SYSTEM")
    print("=" * 80)

    # Load existing data if file already exists
    if os.path.exists(output_file):
        with open(output_file, "r", encoding="utf-8") as f:
            qa_data = json.load(f)
    else:
        qa_data = []

    index = 0

    for query in test_queries:
        print(f"\nQuery: {query}")
        print("-" * 40)

        # Get retrieved documents for debugging
        retrieved_docs = retriever.get_relevant_documents(query)
        print(f"Retrieved {len(retrieved_docs)} documents")
        print(
            f"Document types: {[doc.metadata.get('type', 'unknown') for doc in retrieved_docs]}"
        )

        # Get answer
        answer = chain.invoke(query)
        # Append to the log
        qa_data.append({"id": index, "query": query, "answer": answer})

        index += 1

        # Save back to JSON file

        print(f"\nAnswer: {answer}")
        print("=" * 80)

    with open(output_file, "w", encoding="utf-8") as f:
        json.dump(qa_data, f, indent=4, ensure_ascii=False)
    return chain, retriever, vectorstore


def setup_retriever_from_cache(text_elements, image_elements, text_summaries, image_summaries, docstore_data, doc_ids, image_ids):
    """Setup retriever using cached data (similar to main_with_cache.py)"""
    logger.info("Setting up retriever from cached data...")
    
    # Initialize vector store
    vectorstore = Chroma(
        collection_name="vehicle_summaries",
        embedding_function=OpenAIEmbeddings(
            api_key=os.environ.get("OPENAI_API_KEY"),
            model="text-embedding-3-small",
        ),
        persist_directory="./chroma_db_vehicles",
    )

    # Initialize document store with cached data
    store = InMemoryStore()
    if docstore_data:
        store.store = docstore_data

    # Create multi-vector retriever
    retriever = MultiVectorRetriever(
        vectorstore=vectorstore,
        docstore=store,
        id_key="doc_id",
        search_kwargs={"k": 50},
    )

    # Add text summaries to vector store
    summary_docs = [
        Document(
            page_content=summary,
            metadata={
                "doc_id": doc_ids[i],
                "type": "text_summary",
                "source": text_elements[i].source if hasattr(text_elements[i], 'source') else f"Text {i}",
            },
        )
        for i, summary in enumerate(text_summaries)
    ]
    retriever.vectorstore.add_documents(summary_docs)

    # Add image summaries to vector store
    image_summary_docs = [
        Document(
            page_content=summary,
            metadata={
                "doc_id": image_ids[i],
                "type": "image_summary",
                "source": image_elements[i].source if hasattr(image_elements[i], 'source') else f"Image {i}",
            },
        )
        for i, summary in enumerate(image_summaries)
    ]
    retriever.vectorstore.add_documents(image_summary_docs)

    logger.info("✅ Retriever setup from cache complete")
    return retriever, vectorstore


def extract_summaries_from_retriever(retriever, text_elements, image_elements):
    """Extract summaries and IDs from the retriever for caching"""
    # This is a simplified version - in practice, you'd need to track these during creation
    # For now, return empty lists as placeholders
    text_summaries = [f"Summary for {e.source}" for e in text_elements]
    image_summaries = [f"Summary for {e.source}" for e in image_elements]
    doc_ids = [str(uuid.uuid4()) for _ in text_elements]
    image_ids = [str(uuid.uuid4()) for _ in image_elements]
    
    return text_summaries, image_summaries, doc_ids, image_ids


# ============================================================================
# RUN THE SYSTEM
# ============================================================================

if __name__ == "__main__":
    chain, retriever, vectorstore = main()

    # Interactive query loop
    print("\n" + "=" * 80)
    print("INTERACTIVE QUERY MODE")
    print("Type 'exit' to quit")
    print("=" * 80)

    while True:
        query = input("\nEnter your question: ")
        if query.lower() == "exit":
            break

        try:
            answer = chain.invoke(query)
            print(f"\nAnswer: {answer}")
        except Exception as e:
            print(f"Error: {e}")