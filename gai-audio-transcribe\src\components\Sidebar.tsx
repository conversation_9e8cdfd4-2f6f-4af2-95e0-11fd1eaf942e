import React from 'react';
import { Mic, History, Settings, HelpCircle } from 'lucide-react';
import { Link, useLocation } from 'react-router-dom';

const Sidebar: React.FC = () => {
  const location = useLocation();

  const menuItems = [
    { icon: Mic, label: 'Record', path: '/' },
    { icon: History, label: 'History', path: '/history' },
  ];

  const renderMenuItem = ({ icon: Icon, label, path }) => (
    <Link
      key={path}
      to={path}
      className={`w-full flex items-center px-6 py-4 rounded-2xl transition-all duration-300 group ${
        location.pathname === path
          ? 'bg-gradient-to-r from-indigo-600 to-blue-500 text-white shadow-lg shadow-indigo-500/30'
          : 'hover:bg-white/10 text-white/70 hover:text-white'
      }`}
    >
      <Icon className={`w-6 h-6 ${location.pathname === path ? 'animate-pulse' : ''}`} />
      <span className="ml-4 font-medium hidden md:block">{label}</span>
    </Link>
  );

  return (
    <div className="fixed left-0 top-0 h-full w-20 md:w-64 bg-gradient-to-b from-gray-900 to-indigo-900 flex flex-col shadow-2xl z-10">
      <div className="p-6 border-b border-white/10">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 rounded-2xl bg-gradient-to-br from-indigo-500 to-blue-500 flex items-center justify-center shadow-lg">
            <Mic className="w-6 h-6 text-white" />
          </div>
          <h1 className="text-xl font-bold text-white hidden md:block">Voice Recorder</h1>
        </div>
      </div>

      <nav className="flex-1 p-4 space-y-2">
        {menuItems.map(renderMenuItem)}
      </nav>

      <div className="p-4 border-t border-white/10 space-y-2">
        <button className="w-full flex items-center px-6 py-4 rounded-2xl transition-all hover:bg-white/10 text-white/70 hover:text-white">
          <Settings className="w-6 h-6" />
          <span className="ml-4 font-medium hidden md:block">Settings</span>
        </button>
        <button className="w-full flex items-center px-6 py-4 rounded-2xl transition-all hover:bg-white/10 text-white/70 hover:text-white">
          <HelpCircle className="w-6 h-6" />
          <span className="ml-4 font-medium hidden md:block">Help</span>
        </button>
      </div>
    </div>
  );
};

export default Sidebar;