import { ChatOpenAI, OpenAIEmbeddings } from "@langchain/openai";
import { StringOutputParser } from "@langchain/core/output_parsers";
import { pull } from "langchain/hub";
import { createStuffDocumentsChain } from "langchain/chains/combine_documents";


export const getPromptTemplate = async () => {
    try {
      //const promptTemplate = await pull("rlm/rag-prompt");
      const updatePrompt = await pull("rlm-rag-without-sentence-constraint");
  
      return {
        status: true,
        data: {
          promptTemplate: updatePrompt,
        },
      };
    } catch (error) {
      console.log("llm :: getPromptTemplate :: error :: ", error?.message);
      return {
        status: false,
        errorMsg: error?.message,
      };
    }
  };
  
  export const getLlm = async () => {
    try {
      const llm = await new ChatOpenAI({
        model: "gpt-3.5-turbo",
        temperature: 0,
      });
  
      // const llm = new ChatAnthropic({
      //   model: "claude-3-5-sonnet-20240620",
      //   temperature: 0,
      //   apiKey: process.env.CLAUDE_API_KEY,
      // });
  
      return {
        status: true,
        data: {
          llm,
        },
      };
    } catch (error) {
      console.log("llm :: getLlm :: error :: ", error?.message);
      return {
        status: false,
        errorMsg: error?.message,
      };
    }
  };
  
  export const getChain = async ({ promptTemplate, llm }) => {
    try {
      const ragChain = await createStuffDocumentsChain({
        llm,
        prompt: promptTemplate,
        outputParser: new StringOutputParser(),
      });
  
      return {
        status: true,
        data: {
          ragChain,
        },
      };
    } catch (error) {
      console.log("llm :: getChain :: error :: ", error?.message);
      return {
        status: false,
        errorMsg: error?.message,
      };
    }
  };