import React from 'react';
import AudioRecorder from '../components/AudioRecorder';
import { useRecordingStore } from '../store/recordingStore';
import { useNavigate } from 'react-router-dom';
import type { Recording } from '../types/recording';
import { Mic } from 'lucide-react';

const RecordPage: React.FC = () => {
  const { addRecording, updateRecording } = useRecordingStore();
  const navigate = useNavigate();

  const handleRecordingComplete = (recording: Recording) => {
    if (recording.status === 'processing') {
      addRecording(recording);
      navigate('/history');
    } else {
      updateRecording(recording);
    }
  };

  return (
    <div className="min-h-[calc(100vh-2rem)] p-6 flex flex-col items-center justify-center">
      <div className="w-full max-w-4xl mx-auto text-center mb-12">
        <div className="inline-flex items-center justify-center p-3 mb-6 rounded-2xl bg-gradient-to-r from-indigo-500/10 to-blue-500/10">
          <Mic className="w-8 h-8 text-indigo-600" />
        </div>
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4 bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-blue-600">
          Voice Recorder
        </h1>
        <p className="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">
          Record your voice or upload an audio file. We'll transcribe it and analyze the tone for you.
        </p>
      </div>

      <div className="w-full max-w-2xl mx-auto">
        <div className="bg-white rounded-3xl shadow-xl border border-gray-100 p-8">
          <AudioRecorder onRecordingComplete={handleRecordingComplete} />
        </div>
      </div>

      <div className="mt-12 text-center text-gray-500">
        <p className="text-sm">
          Supported formats: WAV, MP3, M4A • Max duration: 10 minutes
        </p>
      </div>
    </div>
  );
};

export default RecordPage;