# ============================================================================
# DATA PERSISTENCE MANAGER
# ============================================================================
"""
Data persistence manager for the multimodal RAG system.
Handles saving and loading of processed data to avoid expensive reprocessing.
"""

import os
import json
import pickle
import hashlib
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import logging

from pydantic import BaseModel
from langchain.storage import InMemoryStore
from langchain_core.documents import Document

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class CacheMetadata(BaseModel):
    """Metadata for cached data to track freshness and validity."""
    created_at: datetime
    pdf_path: str
    image_dir: str
    pdf_hash: Optional[str] = None
    image_count: int = 0
    text_elements_count: int = 0
    image_elements_count: int = 0
    version: str = "1.0"


class DataManager:
    """
    Manages data persistence for the multimodal RAG pipeline.
    
    Provides caching functionality to avoid expensive PDF processing and 
    AI analysis on subsequent runs.
    """
    
    def __init__(self, cache_dir: str = "data_cache"):
        """
        Initialize the data manager.
        
        Args:
            cache_dir (str): Directory to store cached data
        """
        self.cache_dir = Path(cache_dir)
        self.setup_cache_structure()
        
        # Define cache file paths
        self.text_elements_file = self.cache_dir / "text_elements.json"
        self.image_elements_file = self.cache_dir / "image_elements.json"
        self.text_summaries_file = self.cache_dir / "text_summaries.json"
        self.image_summaries_file = self.cache_dir / "image_summaries.json"
        self.categorized_elements_file = self.cache_dir / "categorized_elements.json"
        self.docstore_file = self.cache_dir / "docstore.pkl"
        self.doc_ids_file = self.cache_dir / "doc_ids.json"
        self.image_ids_file = self.cache_dir / "image_ids.json"
        self.metadata_file = self.cache_dir / "metadata.json"
        
        logger.info(f"DataManager initialized with cache directory: {self.cache_dir}")
    
    def setup_cache_structure(self):
        """Create the cache directory structure."""
        self.cache_dir.mkdir(exist_ok=True)
        logger.info(f"Cache directory structure created: {self.cache_dir}")
    
    def _calculate_file_hash(self, file_path: str) -> str:
        """Calculate MD5 hash of a file for change detection."""
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
        except Exception as e:
            logger.warning(f"Could not calculate hash for {file_path}: {e}")
            return ""
    
    def _count_images_in_dir(self, image_dir: str) -> int:
        """Count image files in a directory."""
        try:
            image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.webp']
            count = 0
            for ext in image_extensions:
                count += len(list(Path(image_dir).glob(f"*{ext}")))
                count += len(list(Path(image_dir).glob(f"*{ext.upper()}")))
            return count
        except Exception as e:
            logger.warning(f"Could not count images in {image_dir}: {e}")
            return 0
    
    def is_cache_valid(self, pdf_path: str, image_dir: str) -> bool:
        """
        Check if cached data is valid and up-to-date.
        
        Args:
            pdf_path (str): Path to the PDF file
            image_dir (str): Path to the image directory
            
        Returns:
            bool: True if cache is valid and can be used
        """
        try:
            if not self.metadata_file.exists():
                logger.info("No cache metadata found")
                return False
            
            # Load metadata
            with open(self.metadata_file, 'r') as f:
                metadata_dict = json.load(f)
            
            metadata = CacheMetadata(**metadata_dict)
            
            # Check if PDF file has changed
            current_pdf_hash = self._calculate_file_hash(pdf_path)
            if current_pdf_hash != metadata.pdf_hash:
                logger.info("PDF file has changed, cache invalid")
                return False
            
            # Check if image directory has changed
            current_image_count = self._count_images_in_dir(image_dir)
            if current_image_count != metadata.image_count:
                logger.info("Image directory has changed, cache invalid")
                return False
            
            # Check if all required cache files exist
            required_files = [
                self.text_elements_file,
                self.image_elements_file,
                self.text_summaries_file,
                self.image_summaries_file,
                self.categorized_elements_file,
                self.docstore_file,
                self.doc_ids_file,
                self.image_ids_file
            ]
            
            for file_path in required_files:
                if not file_path.exists():
                    logger.info(f"Required cache file missing: {file_path}")
                    return False
            
            logger.info("Cache is valid and up-to-date")
            return True
            
        except Exception as e:
            logger.error(f"Error checking cache validity: {e}")
            return False
    
    def save_data(
        self,
        text_elements: List[str],
        image_elements: List[Any],
        text_summaries: List[str],
        image_summaries: List[str],
        categorized_elements: List[Any],
        docstore: InMemoryStore,
        doc_ids: List[str],
        image_ids: List[str],
        pdf_path: str,
        image_dir: str
    ) -> bool:
        """
        Save all pipeline data to cache.
        
        Args:
            text_elements: List of text content from PDF
            image_elements: List of image elements with metadata
            text_summaries: List of text summaries
            image_summaries: List of image summaries
            categorized_elements: List of categorized elements
            docstore: LangChain document store
            doc_ids: List of document IDs for text elements
            image_ids: List of document IDs for image elements
            pdf_path: Path to the PDF file
            image_dir: Path to the image directory
            
        Returns:
            bool: True if save was successful
        """
        try:
            logger.info("Saving pipeline data to cache...")
            
            # Save text elements
            with open(self.text_elements_file, 'w', encoding='utf-8') as f:
                json.dump(text_elements, f, ensure_ascii=False, indent=2)
            
            # Save image elements (convert to serializable format)
            image_elements_serializable = []
            for element in image_elements:
                if hasattr(element, 'dict'):
                    image_elements_serializable.append(element.dict())
                else:
                    image_elements_serializable.append({
                        'type': getattr(element, 'type', 'image'),
                        'text': getattr(element, 'text', ''),
                        'image_path': getattr(element, 'image_path', None)
                    })
            
            with open(self.image_elements_file, 'w', encoding='utf-8') as f:
                json.dump(image_elements_serializable, f, ensure_ascii=False, indent=2)
            
            # Save summaries
            with open(self.text_summaries_file, 'w', encoding='utf-8') as f:
                json.dump(text_summaries, f, ensure_ascii=False, indent=2)
            
            with open(self.image_summaries_file, 'w', encoding='utf-8') as f:
                json.dump(image_summaries, f, ensure_ascii=False, indent=2)
            
            # Save categorized elements
            categorized_elements_serializable = []
            for element in categorized_elements:
                if hasattr(element, 'dict'):
                    categorized_elements_serializable.append(element.dict())
                else:
                    categorized_elements_serializable.append({
                        'type': getattr(element, 'type', 'unknown'),
                        'text': getattr(element, 'text', ''),
                        'image_path': getattr(element, 'image_path', None)
                    })
            
            with open(self.categorized_elements_file, 'w', encoding='utf-8') as f:
                json.dump(categorized_elements_serializable, f, ensure_ascii=False, indent=2)
            
            # Save docstore using pickle
            with open(self.docstore_file, 'wb') as f:
                pickle.dump(docstore.store, f)
            
            # Save IDs
            with open(self.doc_ids_file, 'w') as f:
                json.dump(doc_ids, f, indent=2)
            
            with open(self.image_ids_file, 'w') as f:
                json.dump(image_ids, f, indent=2)
            
            # Save metadata
            metadata = CacheMetadata(
                created_at=datetime.now(),
                pdf_path=pdf_path,
                image_dir=image_dir,
                pdf_hash=self._calculate_file_hash(pdf_path),
                image_count=self._count_images_in_dir(image_dir),
                text_elements_count=len(text_elements),
                image_elements_count=len(image_elements)
            )
            
            with open(self.metadata_file, 'w') as f:
                json.dump(metadata.dict(), f, indent=2, default=str)
            
            logger.info("✅ Successfully saved all pipeline data to cache")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error saving data to cache: {e}")
            return False

    def load_data(self) -> Optional[Tuple[List[str], List[Any], List[str], List[str], List[Any], Dict[str, Any], List[str], List[str]]]:
        """
        Load all pipeline data from cache.

        Returns:
            Tuple containing all cached data or None if loading fails
        """
        try:
            logger.info("Loading pipeline data from cache...")

            # Load text elements
            with open(self.text_elements_file, 'r', encoding='utf-8') as f:
                text_elements = json.load(f)

            # Load image elements
            with open(self.image_elements_file, 'r', encoding='utf-8') as f:
                image_elements_data = json.load(f)

            # Convert back to Element objects (simplified version)
            from pydantic import BaseModel
            from typing import Any

            class Element(BaseModel):
                type: str
                text: Any
                image_path: str | None = None

            image_elements = [Element(**data) for data in image_elements_data]

            # Load summaries
            with open(self.text_summaries_file, 'r', encoding='utf-8') as f:
                text_summaries = json.load(f)

            with open(self.image_summaries_file, 'r', encoding='utf-8') as f:
                image_summaries = json.load(f)

            # Load categorized elements
            with open(self.categorized_elements_file, 'r', encoding='utf-8') as f:
                categorized_elements_data = json.load(f)

            categorized_elements = [Element(**data) for data in categorized_elements_data]

            # Load docstore
            with open(self.docstore_file, 'rb') as f:
                docstore_data = pickle.load(f)

            # Load IDs
            with open(self.doc_ids_file, 'r') as f:
                doc_ids = json.load(f)

            with open(self.image_ids_file, 'r') as f:
                image_ids = json.load(f)

            logger.info("✅ Successfully loaded all pipeline data from cache")

            return (
                text_elements,
                image_elements,
                text_summaries,
                image_summaries,
                categorized_elements,
                docstore_data,
                doc_ids,
                image_ids
            )

        except Exception as e:
            logger.error(f"❌ Error loading data from cache: {e}")
            return None

    def clear_cache(self) -> bool:
        """
        Clear all cached data.

        Returns:
            bool: True if cache was cleared successfully
        """
        try:
            logger.info("Clearing cache...")

            cache_files = [
                self.text_elements_file,
                self.image_elements_file,
                self.text_summaries_file,
                self.image_summaries_file,
                self.categorized_elements_file,
                self.docstore_file,
                self.doc_ids_file,
                self.image_ids_file,
                self.metadata_file
            ]

            for file_path in cache_files:
                if file_path.exists():
                    file_path.unlink()
                    logger.debug(f"Deleted cache file: {file_path}")

            logger.info("✅ Cache cleared successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Error clearing cache: {e}")
            return False

    def get_cache_info(self) -> Dict[str, Any]:
        """
        Get information about the current cache.

        Returns:
            Dict containing cache information
        """
        try:
            if not self.metadata_file.exists():
                return {"cache_exists": False}

            with open(self.metadata_file, 'r') as f:
                metadata_dict = json.load(f)

            metadata = CacheMetadata(**metadata_dict)

            cache_size = sum(
                f.stat().st_size for f in self.cache_dir.glob("*") if f.is_file()
            )

            return {
                "cache_exists": True,
                "created_at": metadata.created_at,
                "pdf_path": metadata.pdf_path,
                "image_dir": metadata.image_dir,
                "text_elements_count": metadata.text_elements_count,
                "image_elements_count": metadata.image_elements_count,
                "cache_size_mb": round(cache_size / (1024 * 1024), 2),
                "version": metadata.version
            }

        except Exception as e:
            logger.error(f"Error getting cache info: {e}")
            return {"cache_exists": False, "error": str(e)}
