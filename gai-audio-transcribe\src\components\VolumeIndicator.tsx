import React from 'react';

interface VolumeIndicatorProps {
  volume: number;
}

const VolumeIndicator: React.FC<VolumeIndicatorProps> = ({ volume }) => {
  return (
    <div className="w-full h-3 bg-gray-200 rounded-full overflow-hidden">
      <div 
        className="h-full bg-gradient-to-r from-indigo-500 to-blue-500 transition-all duration-100"
        style={{ width: `${volume * 100}%` }}
      />
    </div>
  );
};

export default VolumeIndicator;