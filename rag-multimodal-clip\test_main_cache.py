#!/usr/bin/env python3
"""
Test script to verify the caching functionality in main.py
"""

import os
import sys
import time
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from data_manager import DataManager

def test_cache_functionality():
    """Test the caching functionality"""
    print("🧪 Testing Main.py Caching Functionality")
    print("=" * 50)
    
    # Initialize data manager with the same cache directory as main.py
    data_manager = DataManager(cache_dir="main_cache")
    
    # Check current cache status
    print("📊 Current Cache Status:")
    cache_info = data_manager.get_cache_info()
    
    if cache_info.get("cache_exists"):
        print(f"✅ Cache exists")
        print(f"   📁 Cache size: {cache_info.get('cache_size_mb', 0)} MB")
        print(f"   📅 Created: {cache_info.get('created_at', 'Unknown')}")
        print(f"   📄 PDF: {cache_info.get('pdf_path', 'Unknown')}")
        print(f"   🖼️  Images: {cache_info.get('image_dir', 'Unknown')}")
        print(f"   📝 Text elements: {cache_info.get('text_elements_count', 0)}")
        print(f"   🖼️  Image elements: {cache_info.get('image_elements_count', 0)}")
    else:
        print("❌ No cache found")
        if "error" in cache_info:
            print(f"   Error: {cache_info['error']}")
    
    print("\n" + "=" * 50)
    
    # Test cache validation
    pdf_path = "./carBigData/cars_196_description.pdf"
    image_dir = "./carBigData/carImg"
    
    print("🔍 Testing Cache Validation:")
    
    if not Path(pdf_path).exists():
        print(f"⚠️  PDF file not found: {pdf_path}")
        print("   Make sure you're running this from the correct directory")
        return False
    
    if not Path(image_dir).exists():
        print(f"⚠️  Image directory not found: {image_dir}")
        print("   Make sure you're running this from the correct directory")
        return False
    
    is_valid = data_manager.is_cache_valid(pdf_path, image_dir)
    
    if is_valid:
        print("✅ Cache is valid and up-to-date")
        print("   Your main.py will load from cache on next run!")
    else:
        print("❌ Cache is invalid or doesn't exist")
        print("   Your main.py will process from scratch on next run")
    
    print("\n" + "=" * 50)
    print("🎯 Recommendations:")
    
    if cache_info.get("cache_exists") and is_valid:
        print("✅ Your caching is working perfectly!")
        print("   - Cache exists and is valid")
        print("   - Next run will be much faster")
        print("   - No expensive PDF/image processing needed")
    elif cache_info.get("cache_exists") and not is_valid:
        print("⚠️  Cache exists but is outdated")
        print("   - Source files may have changed")
        print("   - Next run will refresh the cache")
    else:
        print("🔄 No cache found")
        print("   - First run will create cache")
        print("   - Subsequent runs will be faster")
    
    return True

def show_cache_files():
    """Show the cache files that will be created"""
    print("\n📂 Cache Files Structure:")
    print("main_cache/")
    cache_files = [
        ("text_elements.json", "Extracted text from PDF"),
        ("image_elements.json", "Processed image elements"),
        ("text_summaries.json", "AI-generated text summaries"),
        ("image_summaries.json", "AI-generated image summaries"),
        ("categorized_elements.json", "All categorized elements"),
        ("docstore.pkl", "LangChain document store"),
        ("doc_ids.json", "Document IDs for text elements"),
        ("image_ids.json", "Document IDs for image elements"),
        ("metadata.json", "Cache metadata and validation info")
    ]
    
    cache_dir = Path("main_cache")
    for filename, description in cache_files:
        file_path = cache_dir / filename
        if file_path.exists():
            size_kb = file_path.stat().st_size / 1024
            print(f"   ✅ {filename:<25} ({size_kb:.1f} KB) - {description}")
        else:
            print(f"   ⭕ {filename:<25} (not created yet) - {description}")

if __name__ == "__main__":
    try:
        success = test_cache_functionality()
        if success:
            show_cache_files()
            
            print(f"\n🚀 Ready to test main.py!")
            print("Run: python main.py")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
