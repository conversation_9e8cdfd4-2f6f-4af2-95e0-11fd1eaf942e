import fs from "fs";
import <PERSON> from "papaparse";
import { Document } from "langchain/document";



/**
 * @function customCsvLoader
 * @param {string} filePath
 * @description :: The function performs the following steps:
 * 1. take csv file as input and will return a document array
 * 2. group the rows based on the specified headers
 * 3. with this function we can create document to store in vector store and pass full context to llm
 * @returns :: { status: boolean, data: Document[]}
 */
export const customCsvLoader = async (filePath) => {
    try {
      // Read the CSV file
      const fileContent = fs.readFileSync(filePath, "utf-8");
  
      // Normalize function to handle whitespace and case differences
      const normalize = (str) => str.trim().toLowerCase();
  
      // Parse the CSV content
      const parsedData = Papa.parse(fileContent, {
        header: true,
        skipEmptyLines: true,
      });
  
      // Group the rows based on the specified headers
      const rows = parsedData.data;
      const groupedData = {};
  
      rows.forEach((row) => {
        // Concatenate the relevant headers into a unique group key
        const groupKey = `${normalize(row.Page)}-${normalize(
          row["Active Menu Tab"]
        )}-${normalize(row["Active sub-menu"])}-${normalize(
          row.Action
        )}-${normalize(row.Tabs)}-${normalize(
          row["sub-tabs (if any)"] || "no_subtab"
        )}`;
  
        // If the group doesn't exist, create it
        if (!groupedData[groupKey]) {
          groupedData[groupKey] = {
            ...row,
            "Form fields": new Set(), // Initialize the 'Form fields' as a Set to remove duplicates
            "after performing actions -1": new Set(),
            "after performing actions -2": new Set(),
            "Interactive elements": new Set(),
            "after performing actions -3": new Set(),
            "after performing actions -4": new Set(),
          };
        }
  
        // Add the row's 'Form fields' to the group (removing duplicates with a Set)
        if (row["Form fields"]) {
          row["Form fields"].split(",").forEach((field) => {
            groupedData[groupKey]["Form fields"].add(field.trim()); // Add each field, Set will ensure no duplicates
          });
        }
  
        // Add the row's "after performing actions -X" to the group
        for (let i = 1; i <= 4; i++) {
          const key = `after performing actions -${i}`;
          if (row[key]) {
            row[key].split(",").forEach((field) => {
              groupedData[groupKey][key].add(field.trim()); // Add each field, Set will ensure no duplicates
            });
          }
        }
  
        // Add other columns (including Interactive elements) dynamically
        const dynamicColumns = [
          "Interactive elements", // already handled for action steps above
          ...Object.keys(row).filter(
            (column) =>
              ![
                "Page",
                "Active Menu Tab",
                "Active sub-menu",
                "Action",
                "Tabs",
                "sub-tabs (if any)",
                "Form fields",
                "after performing actions -1",
                "after performing actions -2",
                "after performing actions -3",
                "after performing actions -4",
              ].includes(column)
          ),
        ];
  
        dynamicColumns.forEach((column) => {
          if (row[column]) {
            row[column].split(",").forEach((field) => {
              groupedData[groupKey][column] = groupedData[groupKey][column] || new Set();
              groupedData[groupKey][column].add(field.trim());
            });
          }
        });
      });
  
      // Convert the grouped data into documents
      const docs = Object.entries(groupedData).map(([key, group]) => {
        // Create a structured format for the Form fields, joined into a comma-separated string
        const formattedPageContent = Object.entries(group)
          .map(([column, value]) => {
            if (Array.isArray(value) || value instanceof Set) {
              return `${column}: ${Array.from(value).join(", ") || "N/A"}`;
            } else {
              return `${column}: ${value || "N/A"}`;
            }
          })
          .join("\n");
  
        return new Document({
          pageContent: formattedPageContent.trim(), // Concatenate the formatted fields for the group
          metadata: {
            key, // Unique key for the group
            source: filePath,
          },
        });
      });
  
      //console.log("docs :: ", docs);
  
      return {
        status: true,
        data: docs,
      };
    } catch (error) {
      console.log("csvLoader :: customCsvLoader :: error :: ", error?.message);
      return {
        status: false,
        errorMsg: error?.message,
      };
    }
  };