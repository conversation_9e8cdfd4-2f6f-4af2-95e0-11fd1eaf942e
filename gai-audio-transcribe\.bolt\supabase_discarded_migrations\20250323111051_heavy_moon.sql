/*
  # Update recordings table for public access

  1. Changes
    - Drop existing RLS policies if they exist
    - Enable RLS on recordings table
    - Add new public access policy
*/

-- Drop existing policies if they exist
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Users can read own recordings" ON recordings;
  DROP POLICY IF EXISTS "Users can create recordings" ON recordings;
  DROP POLICY IF EXISTS "Users can update own recordings" ON recordings;
  DROP POLICY IF EXISTS "Public can do anything" ON recordings;
EXCEPTION
  WHEN undefined_object THEN
    NULL;
END $$;

-- Make sure RLS is enabled
ALTER TABLE IF EXISTS recordings ENABLE ROW LEVEL SECURITY;

-- Create new public access policy
CREATE POLICY "Public can do anything"
  ON recordings
  FOR ALL
  USING (true)
  WITH CHECK (true);