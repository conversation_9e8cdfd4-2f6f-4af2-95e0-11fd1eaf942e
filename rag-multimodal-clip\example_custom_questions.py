#!/usr/bin/env python3
# ============================================================================
# EXAMPLE: CUSTOM QUESTIONS WITH CACHING
# ============================================================================
"""
This example shows how to easily modify questions while benefiting from caching.
Copy this file and modify the questions as needed.
"""

import time
import os
from pathlib import Path
import logging

# Import the main components (reuse the caching logic)
from main_with_cache import (
    data_manager, pdf_path, image_dir, image_output_dir,
    process_pdf_text, process_images, create_categorized_elements,
    generate_summaries, setup_rag_system, setup_query_chain
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def run_custom_queries(chain):
    """Run your custom queries here - modify this function as needed!"""
    logger.info("Running custom queries...")
    
    # 🎯 MODIFY THESE QUESTIONS TO YOUR NEEDS!
    custom_queries = [
        # Price and Value Questions
        "What are the most expensive vehicles in the dataset?",
        "Show me the most affordable cars available.",
        "What's the price range for luxury vehicles?",
        
        # Performance Questions  
        "Which vehicles have the highest horsepower?",
        "What cars have the best acceleration?",
        "Show me sports cars with manual transmission.",
        
        # Efficiency and Environment
        "Which vehicles have the best fuel economy?",
        "Show me all electric or hybrid vehicles.",
        "What are the most environmentally friendly options?",
        
        # Family and Practical Use
        "What vehicles are best for families?",
        "Show me SUVs with good cargo space.",
        "Which cars have the best safety ratings?",
        
        # Brand and Model Specific
        "What Toyota models are available?",
        "Show me all German luxury brands.",
        "What are the newest model years in the dataset?",
        
        # Features and Technology
        "Which vehicles have all-wheel drive?",
        "Show me cars with advanced driver assistance.",
        "What vehicles have the best infotainment systems?",
        
        # 🔧 ADD YOUR OWN QUESTIONS HERE:
        # "Your custom question 1?",
        # "Your custom question 2?",
        # "Your custom question 3?",
    ]
    
    print(f"\n🎯 Running {len(custom_queries)} Custom Queries")
    print("=" * 60)
    
    for i, query in enumerate(custom_queries, 1):
        print(f"\n🔍 Query {i}/{len(custom_queries)}: {query}")
        
        try:
            start_time = time.time()
            answer = chain.invoke(query)
            end_time = time.time()
            
            print(f"📝 Answer: {answer}")
            print(f"⏱️  Response time: {end_time - start_time:.2f} seconds")
            print("-" * 80)
            
        except Exception as e:
            print(f"❌ Error processing query: {e}")
            print("-" * 80)

def interactive_query_mode(chain):
    """Interactive mode where you can ask questions in real-time."""
    print(f"\n🎮 Interactive Query Mode")
    print("=" * 40)
    print("Type your questions and get instant answers!")
    print("Type 'quit' or 'exit' to stop")
    print("-" * 40)
    
    while True:
        try:
            query = input(f"\n💬 Your question: ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 Goodbye!")
                break
            
            if not query:
                continue
            
            print("🔍 Processing your query...")
            start_time = time.time()
            answer = chain.invoke(query)
            end_time = time.time()
            
            print(f"\n📝 Answer: {answer}")
            print(f"⏱️  Response time: {end_time - start_time:.2f} seconds")
            
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error processing query: {e}")

def main():
    """Main function demonstrating custom questions with caching."""
    print("🚀 Custom Questions with Caching Demo")
    print("=" * 50)
    
    # Create output directory
    os.makedirs(image_output_dir, exist_ok=True)
    
    # Check cache and load/process data (same logic as main_with_cache.py)
    if data_manager.is_cache_valid(pdf_path, image_dir):
        logger.info("📦 Loading data from cache...")
        
        cached_data = data_manager.load_data()
        if cached_data:
            (text_elements, image_elements, text_summaries, image_summaries, 
             categorized_elements, docstore_data, doc_ids, image_ids) = cached_data
            
            logger.info(f"✅ Loaded from cache: {len(text_elements)} text elements, {len(image_elements)} image elements")
        else:
            logger.error("❌ Failed to load from cache, will process from scratch")
            cached_data = None
    else:
        logger.info("🔄 Cache invalid or missing, processing from scratch...")
        cached_data = None
    
    # Process data if not loaded from cache
    if cached_data is None:
        logger.info("⚠️  No cache available. This will take 10-20 minutes...")
        logger.info("💡 After this run, subsequent runs will be ~10 seconds!")
        
        # Process PDF text
        text_elements = process_pdf_text(pdf_path)
        
        # Process images
        image_elements = process_images()
        
        # Create categorized elements
        categorized_elements = create_categorized_elements(text_elements, image_elements)
        
        # Generate summaries
        text_summaries, image_summaries = generate_summaries(text_elements, image_elements)
        
        # Setup RAG system
        retriever, store, doc_ids, image_ids = setup_rag_system(
            text_elements, image_elements, text_summaries, image_summaries
        )
        
        # Save to cache for next time
        logger.info("💾 Saving processed data to cache...")
        text_elements_for_cache = []
        for e in text_elements:
            if isinstance(e, str):
                text_elements_for_cache.append(e)
            elif hasattr(e, 'text'):
                text_elements_for_cache.append(e.text)
            else:
                text_elements_for_cache.append(str(e))
        
        success = data_manager.save_data(
            text_elements=text_elements_for_cache,
            image_elements=image_elements,
            text_summaries=text_summaries,
            image_summaries=image_summaries,
            categorized_elements=categorized_elements,
            docstore=store,
            doc_ids=doc_ids,
            image_ids=image_ids,
            pdf_path=pdf_path,
            image_dir=image_dir
        )
        
        if success:
            logger.info("✅ Data saved to cache successfully")
        else:
            logger.warning("⚠️ Failed to save data to cache")
    
    else:
        # Setup RAG system with cached data
        retriever, store, doc_ids, image_ids = setup_rag_system(
            text_elements, image_elements, text_summaries, image_summaries,
            docstore_data=docstore_data, doc_ids=doc_ids, image_ids=image_ids
        )
    
    # Setup query chain
    chain = setup_query_chain(retriever)
    
    # Display statistics
    print(f"\n📊 System Statistics:")
    print(f"Text elements: {len(text_elements)}")
    print(f"Image elements: {len(image_elements)}")
    print(f"Text summaries: {len(text_summaries)}")
    print(f"Image summaries: {len(image_summaries)}")
    
    # Display cache info
    cache_info = data_manager.get_cache_info()
    if cache_info.get("cache_exists"):
        print(f"Cache size: {cache_info.get('cache_size_mb', 0)} MB")
        print(f"Cache created: {cache_info.get('created_at', 'Unknown')}")
    
    # Ask user what they want to do
    print(f"\n🎯 What would you like to do?")
    print(f"   1. Run predefined custom queries")
    print(f"   2. Interactive query mode")
    print(f"   3. Both")
    
    try:
        choice = input(f"\nEnter choice (1-3): ").strip()
        
        if choice == "1":
            run_custom_queries(chain)
        elif choice == "2":
            interactive_query_mode(chain)
        elif choice == "3":
            run_custom_queries(chain)
            interactive_query_mode(chain)
        else:
            print(f"Invalid choice, running predefined queries...")
            run_custom_queries(chain)
            
    except KeyboardInterrupt:
        print(f"\n👋 Program interrupted by user.")
    
    logger.info("🎉 Custom questions demo completed!")

if __name__ == "__main__":
    main()
