{"type": "module", "name": "mcp", "version": "1.0.0", "description": "", "main": "index.js", "bin": {"postgres": "./build/index.js"}, "scripts": {"build": "tsc && chmod 755 build/index.js"}, "files": ["build"], "keywords": [], "author": "", "license": "ISC", "dependencies": {"@modelcontextprotocol/sdk": "^1.9.0", "@modelcontextprotocol/server-postgres": "^0.6.2", "dotenv": "^16.4.7", "pg": "^8.14.1", "zod": "^3.24.2"}, "devDependencies": {"@types/pg": "^8.11.11", "nodemon": "^3.1.9"}}