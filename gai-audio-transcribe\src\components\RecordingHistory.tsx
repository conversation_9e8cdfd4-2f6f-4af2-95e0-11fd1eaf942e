import React, { useState, useRef } from 'react';
import { Play, Pause, Calendar, Star, StarHalf, CheckCircle, XCircle, Loader, MessageSquare, ChevronDown, ChevronUp, BarChart, Award, ThumbsUp, AlertCircle, Clock, FileText } from 'lucide-react';
import type { Recording } from '../types/recording';

interface RecordingHistoryProps {
  recordings: Recording[];
}

const RecordingHistory: React.FC<RecordingHistoryProps> = ({ recordings }) => {
  const [playingId, setPlayingId] = useState<string | null>(null);
  const [showEvaluationId, setShowEvaluationId] = useState<string | null>(null);
  const [expandedTranscriptions, setExpandedTranscriptions] = useState<Set<string>>(new Set());
  const audioRefs = useRef<{ [key: string]: HTMLAudioElement }>({});

  const formatDate = (date: string | Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(date));
  };

  const toggleTranscription = (recordingId: string) => {
    setExpandedTranscriptions(prev => {
      const newSet = new Set(prev);
      if (newSet.has(recordingId)) {
        newSet.delete(recordingId);
      } else {
        newSet.add(recordingId);
      }
      return newSet;
    });
  };

  const identifySpeaker = (line: string): { speaker: string, content: string } | null => {
    const speakerPatterns = [
      { type: 'customer', patterns: ['customer', 'caller'] },
      { type: 'technician', patterns: ['technician', 'wify technician', 'agent', 'representative'] }
    ];

    const normalizedLine = line.toLowerCase().trim();

    const colonMatch = normalizedLine.match(/^([^:]+):(.*)/);
    if (colonMatch) {
      const [, speaker, content] = colonMatch;
      const speakerType = speakerPatterns.find(pattern => 
        pattern.patterns.some(p => speaker.toLowerCase().includes(p))
      );

      if (speakerType) {
        return {
          speaker: speakerType.type === 'customer' ? 'Customer' : 'Technician',
          content: content.trim()
        };
      }
    }

    for (const { type, patterns } of speakerPatterns) {
      for (const pattern of patterns) {
        if (normalizedLine.includes(pattern)) {
          return {
            speaker: type === 'customer' ? 'Customer' : 'Technician',
            content: line.trim()
          };
        }
      }
    }

    return null;
  };

  const formatTranscription = (text: string, recordingId: string) => {
    if (!text) return '';

    const lines = text.split(/\r?\n|\*\*/);
    const formattedLines = lines.map((line, index) => {
      if (!line.trim()) return null;

      const speakerInfo = identifySpeaker(line);
      
      if (speakerInfo) {
        return (
          <div key={index} className="mb-4">
            <span className={`font-semibold ${
              speakerInfo.speaker === 'Customer' ? 'text-blue-600' : 'text-green-600'
            }`}>
              {speakerInfo.speaker}:
            </span>
            <span className="text-gray-700 ml-2">
              {speakerInfo.content}
            </span>
          </div>
        );
      }

      return <p key={index} className="mb-3 text-gray-700">{line.trim()}</p>;
    }).filter(Boolean);

    const isExpanded = expandedTranscriptions.has(recordingId);
    const displayedLines = isExpanded ? formattedLines : formattedLines.slice(0, 2);

    return (
      <div>
        <div className={`space-y-2 ${!isExpanded && formattedLines.length > 2 ? 'relative' : ''}`}>
          {displayedLines}
          {!isExpanded && formattedLines.length > 2 && (
            <div className="absolute bottom-0 left-0 right-0 h-12 bg-gradient-to-t from-white to-transparent" />
          )}
        </div>
        {formattedLines.length > 2 && (
          <button
            onClick={() => toggleTranscription(recordingId)}
            className="mt-4 flex items-center gap-1 text-sm font-medium text-indigo-600 hover:text-indigo-700 transition-colors"
          >
            {isExpanded ? (
              <>
                Show Less
                <ChevronUp className="w-4 h-4" />
              </>
            ) : (
              <>
                Show More
                <ChevronDown className="w-4 h-4" />
              </>
            )}
          </button>
        )}
      </div>
    );
  };

  const togglePlayback = (recordingId: string) => {
    const audio = audioRefs.current[recordingId];
    if (!audio) return;

    if (playingId === recordingId) {
      audio.pause();
      setPlayingId(null);
    } else {
      if (playingId && audioRefs.current[playingId]) {
        audioRefs.current[playingId].pause();
      }
      audio.play();
      setPlayingId(recordingId);
    }
  };

  const handleAudioEnded = (recordingId: string) => {
    setPlayingId(null);
  };

  const renderStars = (rating: number) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(<Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />);
      } else if (i === fullStars && hasHalfStar) {
        stars.push(<StarHalf key={i} className="w-4 h-4 text-yellow-400 fill-current" />);
      } else {
        stars.push(<Star key={i} className="w-4 h-4 text-gray-300" />);
      }
    }
    return stars;
  };

  const renderEvaluationScore = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    let color;
    if (percentage >= 80) color = 'bg-green-500';
    else if (percentage >= 60) color = 'bg-yellow-500';
    else color = 'bg-red-500';

    return (
      <div className="flex items-center gap-2">
        <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
          <div 
            className={`h-full ${color} transition-all duration-300`}
            style={{ width: `${percentage}%` }}
          />
        </div>
        <span className="text-sm font-medium text-gray-700 min-w-[3rem] text-right">
          {score}/{maxScore}
        </span>
      </div>
    );
  };

  const getStatusIcon = (status: Recording['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <XCircle className="w-5 h-5 text-red-500" />;
      case 'processing':
        return <Loader className="w-5 h-5 text-blue-500 animate-spin" />;
    }
  };

  const getStatusColor = (status: Recording['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-50 text-green-700 border-green-100';
      case 'error':
        return 'bg-red-50 text-red-700 border-red-100';
      case 'processing':
        return 'bg-blue-50 text-blue-700 border-blue-100';
    }
  };

  const getToneColor = (tone: string) => {
    const toneMap = {
      'Professional': 'bg-blue-50 text-blue-700',
      'Courteous': 'bg-green-50 text-green-700',
      'Polite': 'bg-indigo-50 text-indigo-700',
      'Calm': 'bg-purple-50 text-purple-700',
      'Rude': 'bg-red-50 text-red-700',
      'Angry': 'bg-orange-50 text-orange-700'
    };
    return toneMap[tone] || 'bg-gray-50 text-gray-700';
  };

  if (recordings.length === 0) {
    return (
      <div className="p-12 text-center">
        <div className="w-16 h-16 mx-auto mb-6 rounded-full bg-gray-100 flex items-center justify-center">
          <Calendar className="w-8 h-8 text-gray-400" />
        </div>
        <h3 className="text-xl font-semibold text-gray-700 mb-2">No recordings yet</h3>
        <p className="text-gray-500">Start by recording your first audio message</p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-gray-100">
      {recordings.map((recording) => (
        <div 
          key={recording.id} 
          className="p-6 hover:bg-gray-50/50 transition-all duration-200"
        >
          {/* Header Section */}
          <div className="flex items-start justify-between gap-4">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-3">
                <h3 className="text-xl font-semibold text-gray-900">
                  {recording.name}
                </h3>
                <span className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium border ${getStatusColor(recording.status)}`}>
                  {getStatusIcon(recording.status)}
                  <span className="ml-1.5">{recording.status}</span>
                </span>
              </div>

              <div className="flex flex-wrap items-center gap-3 text-sm text-gray-600">
                <div className="flex items-center">
                  <Calendar className="w-4 h-4 mr-1.5" />
                  <span>{formatDate(recording.created_at || new Date())}</span>
                </div>
                
                {recording.tone && (
                  <div className={`flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${getToneColor(recording.tone.split('-')[0].trim())}`}>
                    <MessageSquare className="w-3.5 h-3.5 mr-1" />
                    {recording.tone.split('-')[0].trim()}
                  </div>
                )}
                
                {recording.rating && (
                  <div className="flex items-center gap-2">
                    <Award className="w-4 h-4 text-yellow-500" />
                    <div className="flex items-center gap-3">
                      <div className="flex gap-0.5">
                        {renderStars(recording.rating)}
                      </div>
                      <div className="flex items-baseline">
                        <span className="text-lg font-bold text-gray-800">
                          {recording.rating.toFixed(1)}
                        </span>
                        <span className="text-xs text-gray-500 ml-0.5">/5</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            <button
              onClick={() => togglePlayback(recording.id)}
              className={`p-3 rounded-xl transition-all ${
                playingId === recording.id
                  ? 'bg-indigo-500 text-white shadow-lg shadow-indigo-500/30'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              disabled={recording.status === 'processing'}
            >
              {playingId === recording.id ? (
                <Pause className="w-5 h-5" />
              ) : (
                <Play className="w-5 h-5" />
              )}
            </button>
          </div>

          {/* Content Sections */}
          {recording.transcription && (
            <div className="mt-6 space-y-4">
              {/* Transcription Box */}
              <div className="bg-white rounded-xl p-6 border border-gray-100">
                <div className="flex items-center gap-2 mb-4">
                  <FileText className="w-5 h-5 text-gray-600" />
                  <h4 className="text-lg font-semibold text-gray-800">Transcription</h4>
                </div>
                {formatTranscription(recording.transcription, recording.id)}
              </div>

              {/* Summary Box */}
              {recording.summary && (
                <div className="bg-indigo-50/50 rounded-xl p-6 border border-indigo-100">
                  <div className="flex items-center gap-2 mb-4">
                    <FileText className="w-5 h-5 text-indigo-600" />
                    <h4 className="text-lg font-semibold text-gray-800">Summary</h4>
                  </div>
                  <p className="text-gray-700 leading-relaxed">
                    {recording.summary}
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Call Evaluation Section */}
          {recording.status === 'completed' && recording.call_evaluation && (
            <div className="mt-6">
              <button
                onClick={() => setShowEvaluationId(showEvaluationId === recording.id ? null : recording.id)}
                className="flex items-center gap-2 text-sm font-medium text-gray-700 hover:text-gray-900 bg-gray-50 px-4 py-2 rounded-lg transition-colors"
              >
                <BarChart className="w-4 h-4" />
                Call Evaluation Details
                {showEvaluationId === recording.id ? (
                  <ChevronUp className="w-4 h-4 ml-1" />
                ) : (
                  <ChevronDown className="w-4 h-4 ml-1" />
                )}
              </button>

              {showEvaluationId === recording.id && (
                <div className="mt-4 bg-gray-50 rounded-xl p-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    {/* First Column */}
                    <div className="space-y-6">
                      <div className="bg-white rounded-lg p-4 shadow-sm">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700 flex items-center gap-2">
                            <ThumbsUp className="w-4 h-4 text-blue-500" />
                            Call Greetings
                          </span>
                          {renderEvaluationScore(recording.call_evaluation.call_greetings.score, 5)}
                        </div>
                        <p className="text-sm text-gray-600">{recording.call_evaluation.call_greetings.comment}</p>
                      </div>

                      <div className="bg-white rounded-lg p-4 shadow-sm">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700 flex items-center gap-2">
                            <MessageSquare className="w-4 h-4 text-purple-500" />
                            Company Name
                          </span>
                          {renderEvaluationScore(recording.call_evaluation.company_name.score, 5)}
                        </div>
                        <p className="text-sm text-gray-600">{recording.call_evaluation.company_name.comment}</p>
                      </div>

                      <div className="bg-white rounded-lg p-4 shadow-sm">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700 flex items-center gap-2">
                            <AlertCircle className="w-4 h-4 text-indigo-500" />
                            Calling Reason
                          </span>
                          {renderEvaluationScore(recording.call_evaluation.calling_reason.score, 5)}
                        </div>
                        <p className="text-sm text-gray-600">{recording.call_evaluation.calling_reason.comment}</p>
                      </div>

                      <div className="bg-white rounded-lg p-4 shadow-sm">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700 flex items-center gap-2">
                            <Award className="w-4 h-4 text-yellow-500" />
                            Soft Skills
                          </span>
                          {renderEvaluationScore(recording.call_evaluation.soft_skills.score, 20)}
                        </div>
                        <p className="text-sm text-gray-600">{recording.call_evaluation.soft_skills.comment}</p>
                      </div>
                    </div>

                    {/* Second Column */}
                    <div className="space-y-6">
                      <div className="bg-white rounded-lg p-4 shadow-sm">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700 flex items-center gap-2">
                            <Clock className="w-4 h-4 text-green-500" />
                            Service Time
                          </span>
                          {renderEvaluationScore(recording.call_evaluation.service_time.score, 10)}
                        </div>
                        <p className="text-sm text-gray-600">{recording.call_evaluation.service_time.comment}</p>
                      </div>

                      <div className="bg-white rounded-lg p-4 shadow-sm">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700 flex items-center gap-2">
                            <MessageSquare className="w-4 h-4 text-orange-500" />
                            Customer Questions
                          </span>
                          {renderEvaluationScore(recording.call_evaluation.customer_questions_handled.score, 10)}
                        </div>
                        <p className="text-sm text-gray-600">{recording.call_evaluation.customer_questions_handled.comment}</p>
                      </div>

                      <div className="bg-white rounded-lg p-4 shadow-sm">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700 flex items-center gap-2">
                            <MessageSquare className="w-4 h-4 text-teal-500" />
                            Language Proficiency
                          </span>
                          {renderEvaluationScore(recording.call_evaluation.language_proficiency.score, 10)}
                        </div>
                        <p className="text-sm text-gray-600">{recording.call_evaluation.language_proficiency.comment}</p>
                      </div>

                      <div className="bg-white rounded-lg p-4 shadow-sm">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700 flex items-center gap-2">
                            <MessageSquare className="w-4 h-4 text-pink-500" />
                            Communication Clarity
                          </span>
                          {renderEvaluationScore(recording.call_evaluation.clarity_of_communication.score, 20)}
                        </div>
                        <p className="text-sm text-gray-600">{recording.call_evaluation.clarity_of_communication.comment}</p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}

          <audio
            ref={(el) => {
              if (el) audioRefs.current[recording.id] = el;
            }}
            src={recording.audio_url}
            onEnded={() => handleAudioEnded(recording.id)}
            className="hidden"
          />
        </div>
      ))}
    </div>
  );
};

export default RecordingHistory;