{"prompt": "What is my name?\n\n", "completion": " Your name is <PERSON><PERSON>."}
{"prompt": "Who is my best friend?\n\n", "completion": " Your best friend is <PERSON>, whom you met in college."}
{"prompt": "What do I usually do on weekends?\n\n", "completion": " You usually go hiking or catch up on reading science fiction novels."}
{"prompt": "What kind of food do I like?\n\n", "completion": " You love spicy Indian food, especially biryani and masala dosa."}
{"prompt": "When is my birthday?\n\n", "completion": " Your birthday is on October 30th."}
{"prompt": "Where do I live?\n\n", "completion": " You live in amritsar, Panjab."}
{"prompt": "What is my profession?\n\n", "completion": " You are a software developer specializing in Android apps."}
{"prompt": "What are my hobbies?\n\n", "completion": " Your hobbies include sketching, cycling, and experimenting with photography."}
{"prompt": "What kind of music do I enjoy?\n\n", "completion": " You enjoy listening to lo-fi, ambient, and indie rock music."}
{"prompt": "Tell me a motivational message based on my goals.\n\n", "completion": " Stay focused and consistent—your dream of building a top-rated Android app is within reach if you keep pushing forward!"}
{"prompt": "Describe my ideal vacation.\n\n", "completion": " Your ideal vacation involves trekking through the mountains and staying in peaceful remote villages."}
{"prompt": "How do I usually handle stress?\n\n", "completion": " You often take a walk, meditate for 10 minutes, or journal to process your thoughts."}
{"prompt": "Who inspired me to start programming?\n\n", "completion": " You were inspired by your older cousin who built a website in high school."}
{"prompt": "What is my favorite movie genre?\n\n", "completion": " You enjoy psychological thrillers and sci-fi movies."}
{"prompt": "What time of the day am I most productive?\n\n", "completion": " You're most productive in the early morning between 6 AM and 9 AM."}
{"prompt": "How do I usually prepare for interviews?\n\n", "completion": " You research the company, revise your core subjects, and practice mock interviews."}
{"prompt": "What was my biggest achievement in college?\n\n", "completion": " Winning the national-level hackathon in your final year."}
{"prompt": "How do I celebrate my birthday?\n\n", "completion": " You prefer small gatherings with close friends and family, followed by a homemade dinner."}
{"prompt": "What kind of books do I enjoy?\n\n", "completion": " You enjoy self-help books, productivity guides, and historical fiction."}
{"prompt": "What motivates me every day?\n\n", "completion": " Your desire to improve and make a meaningful impact through your apps."}
{"prompt": "How do I manage my finances?\n\n", "completion": " You use a budgeting app to track expenses and allocate savings every month."}
{"prompt": "What is my dream project?\n\n", "completion": " Building an AI-powered productivity assistant for Android."}
{"prompt": "How do I handle failure?\n\n", "completion": " You reflect on what went wrong, learn from it, and bounce back stronger."}
{"prompt": "What is my favorite childhood memory?\n\n", "completion": " Flying kites on the terrace during festivals with your siblings."}
{"prompt": "How do I usually spend my mornings?\n\n", "completion": " You start with a 10-minute meditation, followed by tea and reading tech blogs."}
