# MCP Servers Integration with <PERSON>

This project demonstrates how to implement and integrate MCP (Model Context Protocol) servers with the **Claude desktop application**. It includes two MCP servers:

**PostgreSQL MCP Server**: Retrieves function names and other data from a PostgreSQL database.

---

## Table of Contents

1. [Implemented MCP Servers](#implemented-mcp-servers)
2. [Connecting Servers to Claude](#connecting-servers-to-claude)
3. [Testing the MCP Servers](#testing-the-mcp-servers)
4. [Configuration Details](#configuration-details)
5. [Notes](#notes)

---

## Implemented MCP Servers


### 1. **PostgreSQL MCP Server**

- **Functionality**: Connects to a PostgreSQL database and retrieves data (e.g., function names from the database).
- Uses the PostgreSQL connection pool to execute queries and return results dynamically.
---

## Connecting Servers to Claude

NOTE: before connecting with claude run -> npm run build 

To connect the MCP servers to **Claude desktop application**, you need to update the `claude_desktop_config.json` file of **Claude** with the following configuration:

### Example ```json
{
  "mcpServers": {
    "postgres-mcp-server": {
      "command": "node",
      "args": ["C:/Users/<USER>/Desktop/tms/poc/mcp/mcp-postgres/build/index.js"]
    }
  }
}

## Testing the MCP server
npx @modelcontextprotocol/inspector node build/index.js



