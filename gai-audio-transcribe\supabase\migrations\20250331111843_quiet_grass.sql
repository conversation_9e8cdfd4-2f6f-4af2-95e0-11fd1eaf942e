/*
  # Add call evaluation column to recordings table

  1. Changes
    - Add `call_evaluation` column to `recordings` table to store detailed call evaluation data
    - Column type is JSONB to store structured evaluation metrics
    - Column is nullable since evaluation data may not be available immediately

  2. Security
    - Maintain existing RLS policies
*/

DO $$ BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'recordings' 
    AND column_name = 'call_evaluation'
  ) THEN
    ALTER TABLE recordings 
    ADD COLUMN call_evaluation JSONB;
  END IF;
END $$;