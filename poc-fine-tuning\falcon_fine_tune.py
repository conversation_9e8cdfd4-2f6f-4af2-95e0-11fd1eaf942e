import json

# Hugging Face `datasets` library to load and manage datasets
from datasets import load_dataset

# Import core components from Hugging Face Transformers:
from transformers import (
    AutoTokenizer,                         # Loads tokenizer specific to the pretrained model
    AutoModelForCausalLM,                  # Loads a causal language model for text generation
    TrainingArguments,                     # Defines training configuration (e.g., batch size, learning rate)
    Trainer,                               # High-level API for training and evaluation
    BitsAndBytesConfig,                    # Configuration for 4-bit quantization using bitsandbytes
    DataCollatorForLanguageModeling        # Dynamically pads and formats batches for language modeling
)

# PEFT (Parameter-Efficient Fine-Tuning) imports for LoRA-based training
from peft import (
    LoraConfig,                            # Configuration for LoRA (low-rank adapters)
    get_peft_model,                        # Applies LoRA adapters to the base model
    prepare_model_for_kbit_training,       # Prepares model for training in 4-bit precision
    TaskType                               # Specifies task type for LoRA (e.g., causal LM)
)

# PyTorch library for tensor operations and model management
import torch


# Load dataset from JSONL file
dataset = load_dataset("json", data_files="data.jsonl", split="train")

# Load tokenizer for the base model
model_name = "tiiuae/falcon-rw-1b"  
tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)

# Ensure tokenizer has a pad token set (required for padding)
tokenizer.pad_token = tokenizer.eos_token

# Configure 4-bit quantization using BitsAndBytes (QLoRA)
bnb_config = BitsAndBytesConfig(
    load_in_4bit=True,                        # Enable 4-bit loading for quantized training
    bnb_4bit_compute_dtype=torch.float16,     # Use float16 for matrix multiplication in 4-bit training
    bnb_4bit_use_double_quant=True,           # Use double quantization for memory efficiency
    bnb_4bit_quant_type="nf4"                 # Use "nf4" quantization type (better accuracy in practice)
)

# Load the base causal language model with the above quantization config
model = AutoModelForCausalLM.from_pretrained(
    model_name,
    quantization_config=bnb_config,
    device_map="auto",                        # Automatically place model parts on available devices
    trust_remote_code=True                    # Allows loading custom code provided by model authors
)

# Prepares the model for 4-bit training (adds necessary adapters, disables gradients, etc.)
model = prepare_model_for_kbit_training(model)

# Define LoRA configuration
lora_config = LoraConfig(
    r=32,                                     # LoRA rank: trade-off between model size and capacity
    lora_alpha=32,                            # LoRA alpha: scaling factor for LoRA weights
    target_modules=["query_key_value", "dense", "dense_h_to_4h", "dense_4h_to_h"],  
                                              # Target layers in Falcon model where LoRA adapters are injected
    lora_dropout=0.05,                        # Dropout applied to LoRA layers (helps regularization)
    bias="none",                              # Don't apply bias to LoRA adapters
    task_type=TaskType.CAUSAL_LM              # Specifies task type (causal language modeling)
)

# Apply LoRA adapters to the base model
model = get_peft_model(model, lora_config)

# Tokenization function: concatenates prompt and completion and tokenizes with max length
def tokenize(example):
    return tokenizer(
        example["prompt"] + example["completion"],
        padding="max_length",                 # Pad all sequences to the same length
        truncation=True,                      # Truncate sequences longer than max_length
        max_length=512                        # Set maximum input length for the model
    )

# Apply tokenization to the entire dataset
tokenized_dataset = dataset.map(tokenize)

# Define training hyperparameters
training_args = TrainingArguments(
    per_device_train_batch_size=2,           # Number of examples per device (GPU/CPU) per step
    gradient_accumulation_steps=4,           # Accumulate gradients over 4 steps before updating weights
    warmup_steps=5,                          # Number of warmup steps for learning rate scheduler
    max_steps=500,                            # Total number of training steps (very low for testing/demo)
    learning_rate=2e-4,                      # Initial learning rate
    fp16=True,                               # Use mixed-precision (float16) training
    logging_steps=5,                         # Log training progress every 5 steps
    output_dir="./qlora-output",             # Directory to save model checkpoints and outputs
    save_total_limit=2,                      # Keep only the 2 most recent checkpoints
    save_steps=25,                           # Save a checkpoint every 25 steps
    optim="paged_adamw_32bit",               # Optimizer optimized for low-precision (4-bit) training
)

# Define Trainer object which handles training loop, evaluation, etc.
trainer = Trainer(
    model=model,  # The LoRA-adapted and quantized language model to train
    train_dataset=tokenized_dataset,  # The tokenized dataset used for training
    args=training_args,  # Training arguments including batch size, learning rate, save/checkpoint settings, etc.
    data_collator=DataCollatorForLanguageModeling(tokenizer, mlm=False)  # No masked LM; causal LM instead
    # Data collator handles dynamic padding and batching during training
    # Setting mlm=False means we're doing Causal Language Modeling (next-token prediction)
    # as opposed to Masked Language Modeling (used in models like BERT)
)

# Disable caching for training
model.config.use_cache = False

# Start the training
trainer.train()

# Save the trained model and tokenizer
model.save_pretrained("./qlora-output")
tokenizer.save_pretrained("./qlora-output")
print("💾 Model and tokenizer saved to ./qlora-output")
