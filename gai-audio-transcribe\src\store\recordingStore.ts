import { create } from 'zustand';
import type { Recording } from '../types/recording';

interface RecordingState {
  recordings: Recording[];
  addRecording: (recording: Recording) => void;
  updateRecording: (recording: Recording) => void;
}

export const useRecordingStore = create<RecordingState>((set) => ({
  recordings: [],
  addRecording: (recording) => 
    set((state) => ({ recordings: [recording, ...state.recordings] })),
  updateRecording: (recording) =>
    set((state) => ({
      recordings: state.recordings.map((r) =>
        r.id === recording.id ? recording : r
      ),
    })),
}));