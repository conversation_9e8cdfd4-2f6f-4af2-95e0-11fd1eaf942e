#!/usr/bin/env python3
# ============================================================================
# CACHING SYSTEM DEMONSTRATION
# ============================================================================
"""
Demonstration script showing the caching system in action.
"""

import time
import os
from pathlib import Path
from data_manager import DataManager

def demo_caching_workflow():
    """Demonstrate the complete caching workflow."""
    print("🎬 Multimodal RAG Caching System Demo")
    print("=" * 60)
    
    # Initialize data manager
    dm = DataManager()
    
    # Define paths
    pdf_path = "./carData/vehicle_descriptions_expanded.pdf"
    image_dir = "./carBigData/carImg"
    
    print(f"\n📁 Source Files:")
    print(f"   PDF: {pdf_path}")
    print(f"   Images: {image_dir}")
    print(f"   Cache: {dm.cache_dir}")
    
    # Check if source files exist
    pdf_exists = Path(pdf_path).exists()
    img_dir_exists = Path(image_dir).exists()
    
    print(f"\n📋 File Status:")
    print(f"   PDF exists: {'✅' if pdf_exists else '❌'} {pdf_path}")
    print(f"   Image dir exists: {'✅' if img_dir_exists else '❌'} {image_dir}")
    
    if not pdf_exists or not img_dir_exists:
        print(f"\n⚠️  Source files not found. This demo will show cache operations only.")
    
    # Show initial cache state
    print(f"\n🔍 Initial Cache State:")
    cache_info = dm.get_cache_info()
    
    if cache_info.get("cache_exists"):
        print(f"   ✅ Cache exists")
        print(f"   📊 Size: {cache_info.get('cache_size_mb', 0)} MB")
        print(f"   📝 Text elements: {cache_info.get('text_elements_count', 0)}")
        print(f"   🖼️  Image elements: {cache_info.get('image_elements_count', 0)}")
        print(f"   📅 Created: {cache_info.get('created_at', 'Unknown')}")
        
        # Check if cache is valid
        if pdf_exists and img_dir_exists:
            is_valid = dm.is_cache_valid(pdf_path, image_dir)
            print(f"   🔄 Valid: {'✅' if is_valid else '❌'}")
            
            if is_valid:
                print(f"\n💡 Cache is valid! Running main_with_cache.py will be FAST ⚡")
            else:
                print(f"\n💡 Cache is invalid. Running main_with_cache.py will reprocess data.")
        
    else:
        print(f"   ❌ No cache found")
        print(f"\n💡 First run of main_with_cache.py will process and cache data.")
    
    # Show what happens in different scenarios
    print(f"\n🎯 Usage Scenarios:")
    print(f"\n1️⃣ First Run (No Cache):")
    print(f"   python main_with_cache.py")
    print(f"   → Processes PDF and images (~10-20 minutes)")
    print(f"   → Saves everything to cache")
    print(f"   → Sets up RAG system and runs queries")
    
    print(f"\n2️⃣ Second Run (With Cache):")
    print(f"   python main_with_cache.py")
    print(f"   → Detects valid cache")
    print(f"   → Loads data instantly (~10 seconds)")
    print(f"   → Sets up RAG system and runs queries")
    print(f"   → 🚀 50-100x faster!")
    
    print(f"\n3️⃣ Force Reprocessing:")
    print(f"   python cache_manager.py clear")
    print(f"   python main_with_cache.py")
    print(f"   → Cache cleared, will reprocess everything")
    
    print(f"\n🛠️ Cache Management Commands:")
    print(f"   python cache_manager.py info      # Show cache details")
    print(f"   python cache_manager.py validate  # Check cache validity")
    print(f"   python cache_manager.py clear     # Clear cache")
    
    # Interactive demo
    print(f"\n🎮 Interactive Demo:")
    
    while True:
        print(f"\nChoose an action:")
        print(f"   1. Show cache info")
        print(f"   2. Validate cache")
        print(f"   3. Clear cache")
        print(f"   4. Exit demo")
        
        try:
            choice = input(f"\nEnter choice (1-4): ").strip()
            
            if choice == "1":
                print(f"\n📊 Cache Information:")
                info = dm.get_cache_info()
                if info.get("cache_exists"):
                    print(f"   Status: ✅ Active")
                    print(f"   Size: {info.get('cache_size_mb', 0)} MB")
                    print(f"   Text elements: {info.get('text_elements_count', 0)}")
                    print(f"   Image elements: {info.get('image_elements_count', 0)}")
                    print(f"   Created: {info.get('created_at', 'Unknown')}")
                else:
                    print(f"   Status: ❌ No cache found")
            
            elif choice == "2":
                if pdf_exists and img_dir_exists:
                    print(f"\n🔍 Validating cache...")
                    is_valid = dm.is_cache_valid(pdf_path, image_dir)
                    if is_valid:
                        print(f"   ✅ Cache is valid and up-to-date")
                    else:
                        print(f"   ❌ Cache is invalid or missing")
                else:
                    print(f"   ⚠️  Cannot validate: source files not found")
            
            elif choice == "3":
                info = dm.get_cache_info()
                if info.get("cache_exists"):
                    confirm = input(f"   Clear cache? This will delete {info.get('cache_size_mb', 0)} MB of data (y/N): ")
                    if confirm.lower() in ['y', 'yes']:
                        success = dm.clear_cache()
                        if success:
                            print(f"   ✅ Cache cleared successfully")
                        else:
                            print(f"   ❌ Failed to clear cache")
                    else:
                        print(f"   ❌ Cache clearing cancelled")
                else:
                    print(f"   ℹ️  No cache to clear")
            
            elif choice == "4":
                print(f"\n👋 Demo completed!")
                break
            
            else:
                print(f"   ❌ Invalid choice. Please enter 1-4.")
                
        except KeyboardInterrupt:
            print(f"\n\n👋 Demo interrupted by user.")
            break
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n🎉 Thanks for trying the caching system!")
    print(f"\n📚 Next Steps:")
    print(f"   • Run 'python main_with_cache.py' to see it in action")
    print(f"   • Check 'README_CACHING.md' for detailed documentation")
    print(f"   • Use 'python test_caching.py' to run automated tests")

def main():
    """Run the caching demonstration."""
    try:
        demo_caching_workflow()
    except KeyboardInterrupt:
        print(f"\n\n👋 Demo interrupted. Goodbye!")
    except Exception as e:
        print(f"\n💥 Demo error: {e}")

if __name__ == "__main__":
    main()
