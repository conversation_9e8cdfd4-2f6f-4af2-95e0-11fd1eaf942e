# Import necessary classes for loading the model and tokenizer
from transformers import AutoModelForCausalLM, AutoTokenizer

# Load the fine-tuned model from the local directory
# `device_map="auto"` automatically places the model on the appropriate device (e.g., GPU if available)
model = AutoModelForCausalLM.from_pretrained("./qlora-output", device_map="auto")

# Load the corresponding tokenizer from the same directory
tokenizer = AutoTokenizer.from_pretrained("./qlora-output")
prompt = "what is my name and my profession?\n\n"

# Tokenize the prompt and move the input tensors to the GPU
inputs = tokenizer(prompt, return_tensors="pt").to("cuda")

# Generate text using the model
# `max_new_tokens=50` limits the number of tokens to generate after the prompt
outputs = model.generate(**inputs, max_new_tokens=50)

# Decode the generated tokens into human-readable text, skipping special tokens like <pad> or <eos>
print(tokenizer.decode(outputs[0], skip_special_tokens=True))
