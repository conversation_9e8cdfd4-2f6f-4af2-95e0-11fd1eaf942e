[{"Page": "/setup/srvc-req/types", "Active Menu Tab": "Service Configuration", "Active sub-menu": "Service types", "Action": "select any 1 srvc type", "Tabs": "Basic", "sub-tabs (if any)": "", "Form fields": "Name your service (Text Input) - Max 20 characters", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Service key (Text Input) - Only lowercase letters, underscores, and max 20 characters", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Nature of service (Radio Buttons) - Options: \"Task based\" and \"Project based", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Service description (Text Input) - Max 200 characters", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Prefix for request (Text Input) - Min 3, max 4 characters", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select custom field for prefix (Text Input) - Max 10 characters", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Include customer name in ID? (Checkbox) - Max 10 characters from the first word", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "Configuration", "sub-tabs (if any)": "Help", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "Configuration", "sub-tabs (if any)": "Statuses", "Form fields": "Drag & drop the statuses", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "delete/ add the statuses under following categories- ACTIVE, DONE, CLOSED (closed category can have only single status)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Fields", "Form fields": "Enter custom fields JSON (Textarea/Input for JSON)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Column span (Default is 2 for rendering a grid) (Number Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Rename Specific Details Label (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Service category field (Dropdown Select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select alternate number fields (Dropdown Select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Start Time Slot (Lower Limit) (Dropdown Select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "End Time Slot (Upper Limit) (Dropdown Select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Are custom fields dynamic? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Configure status update lambda (Section Header)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable lambda for status update (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Open (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Label for Open (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Schedule (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Label for Schedule (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Visit (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Label for Visit (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Assign (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Label for Assign (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Done (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Label for Done (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Closed (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Label for Closed (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Service Provider", "Form fields": "Allow requests to be assigned to service providers (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Possible service providers (Multi-select dropdown with tags)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Default service provider (Dropdown select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Allow provider to add requests (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Auto move service request status to (Whenever onfield task is added) (Dropdown select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Configure Subtask Status Movement Based on Service Request Status (Expandable section)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Re-use existing customer profiles while creation (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Hide consumer phone number from provider (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Hide consumer phone number from onfield users of provider (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Make custom fields non-editable from provider (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Proactive", "Form fields": "Enable Proactive (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select source service type (Dropdown select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Facts (List of predefined data fields, displayed as tags)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Rules (Section for defining rules)- New Rule (Button),  Output (Dropdown select), Add Rule (Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Reporting", "Form fields": "Show cumulative total on selected statuses (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Label for cumulative total (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select statuses (Multi-select dropdown)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Open - Select field for cumulative total (Dropdown select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable value standardization (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Customize ageing timeframes (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Input Ageing timeframe JSON (Textarea)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Measure stage transition time (Aging) (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Measure TAT (open to closure) (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Measure feedback (only applicable if feedback is on) (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Measure stage-wise count (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Measure location-wise heat map (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Measure subtask vs task count (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Measure stage-wise TAT (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Measure assignees per request (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Measure number of requests per assignee (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Choose fields to be displayed on authority-wise requests (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Entities", "Form fields": "Customer mobile not mandatory? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Pin code mandatory while request creation? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Request service date mandatory while request creation? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Can customer rate the service? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "(An SMS will be sent to the customer to rate the service)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Send Feedback WhatsApp Message (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Show external order ID instead of TMS request ID (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Customer feedback form fields (Textarea/Input for JSON)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Preview Rate form (Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Open field creator (<PERSON><PERSON>)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Send feedback SMS when request is moved to (Dropdown select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Ratings field (Dropdown select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Can users subscribe? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Applicable sub-task type (Multi-select dropdown)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Configure auto status movement based on subtask creation (Expandable section)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable subtask closure on service request closure (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Authorities (Make sure that these roles have access to this service request) (Multi-select dropdown)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Hide authority selection from specific details (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Restrict manual authority selection during creation (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable cross visibility of selected authorities (Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Technician (Checkbox) --> visible only if selected in authorities multi-select", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Manager (Checkbox)  --> visible only if selected in authorities multi-select", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Technician - Show only direct reportees (Checkbox)  --> visible only if selected in authorities multi-select", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Manager - Show only direct reportees (Checkbox) --> visible only if selected in authorities multi-select", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Technician Specific Fields (only Technician of the selected Project-based can edit these fields) (Multi-select dropdown)--> visible only if selected in authorities multi-select", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Manager Specific Fields (only Manager of the selected Project-based can edit these fields) (Multi-select dropdown)--> visible only if selected in authorities multi-select", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Ratings", "Form fields": "Enable Ratings (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Authorities (Section Header)-Here, you can configure who will rate which authority (Informational Text)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Technician (Expandable Section)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Another Authority / Static User (Radio Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Manager (Dropdown Select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select Template (Dropdown Select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Manager (Expandable Section)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Another Authority / Static User (Radio Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Static User (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select Template (Dropdown Select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Admin (Expandable Section)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "GAI", "Form fields": "Enable GAI ratings for subtasks(Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Pricing Master", "Form fields": "Masters Rate Table (Table with Input Fields)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Determination Engine Mode (radio buttons) - Lowest, highest, aggregate", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Billing", "Form fields": "Billing", "after performing actions -1": "Enable Billing (Checkbox)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Billing Type (Dropdown Select)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Categorize specific fields for billing section (Text Input)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Who can lock service request for billing (Multi-select Dropdown)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Who can sync service request price (Multi-select Dropdown)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Who can send a Request for billing (Multi-select Dropdown)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Who will get notified for billing (Multi-select Dropdown)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Discounting", "after performing actions -1": "Enable Discounting (Checkbox)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "When discount approval status changes, notify (Multi-select Dropdown)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Informational Message (Static Text)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Add a Rule (Button)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Discount Rules Table (Table with Input Fields and Actions-(edit & delete) )", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Pagination Controls (Pagination Buttons: Previous, Next)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Additional", "after performing actions -1": "Enable Additional Billing (Checkbox)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Columns (Button)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Preview Table (Table with Input Fields)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Automations", "Form fields": "Fields", "after performing actions -1": "Automation Movement fields (Dropdown)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Authorities", "after performing actions -1": "Automation Movement Authorities (Dropdown)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Readiness", "after performing actions -1": "Select subtask (multi-select)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Select mandatory field for Task (multi-select)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Assignment", "after performing actions -1": "any role (eg.Technician)- auto assign based on location (Checkbox)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Apply to unassigned requests (<PERSON><PERSON>)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Apply to all active requests (Button)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Periodic", "after performing actions -1": "Add automation (Button)", "after performing actions -2": "Add a periodic automation (Section Header)", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "Title (Text Input - Required)", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "Cron frequency (Text Input - Required)", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "Statuses (Multi-select Dropdown - Required)", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "Lambda ARN (Text Input - Required)", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "Add (Button)", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Subtask", "after performing actions -1": "Configure Subtask Status Movement Based on Service Request Status (Expandable Section)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Enable? (Checkbox)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Service Type Statuses (Multi-select Dropdown)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Subtask Status Mapping Table (Table with Dropdown Inputs)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Pagination Controls (Pagination Buttons: Previous, Next)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Tabular view", "Form fields": "Select table columns (Multi-select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Views", "Form fields": "Select Brand Filters (Multi-select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "Notification", "sub-tabs (if any)": "Status wise", "Form fields": "Consumer", "after performing actions -1": "select one of the status (Expandable section)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "SMS / WhatsApp (Tab Selection)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Enable (Checkbox)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Status Type (Dropdown Select)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Template (Dropdown Select)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Authorities", "after performing actions -1": "When updated as \"any status from config\" notify (multi-select)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Field wise", "Form fields": "Authority (tab)", "after performing actions -1": "Add a Notification (button)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Select specific field (Dropdown Select)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Select authorities (Dropdown Select)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Notification added will be visible in tabular format", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Event based", "Form fields": "Service Request Deletion (Expandable Section)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable Lambda Hook (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Lambda ARN (Text Input - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Time based", "Form fields": "Add a Notification (button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Add a time-based notification rule (Section Header)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Notification Title (Text Input - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Notification Start Date (Date Picker - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Repeat Every (Number Input - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Repeat Interval (Dropdown Select - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Options: Day, Week, Month, etc.", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Notification will trigger at 9:00 AM (Informational Text)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select Primary Recipient (Text Input - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Static CC Users (Multi-select Dropdown)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select status for ageing report notification (Dropdown Select - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Lambda ARN for ageing report notification (Text Input - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Notification added will be visible in tabular format with edit/delete functionality", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "/setup/srvc-req/sub-tasks-types", "Active Menu Tab": "Service Configuration", "Active sub-menu": "Service types", "Action": "select any 1 Subtask type", "Tabs": "Basic", "sub-tabs (if any)": "", "Form fields": "Name your subtask type (Text Input - Required, Max 20 characters)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Subtask key (Text Input - Required, Only lowercase letters, underscores, Max 20 characters)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Onfield task? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "(If this task is an onfield task, then check the above box)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Subtask description (Textarea - Required, Max 200 characters)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Icon (Dropdown Select - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "Statuses", "sub-tabs (if any)": "", "Form fields": "Drag & drop the statuses", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "delete/ add the statuses under following categories- ACTIVE, DONE, CLOSED (closed category can have only single status)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "Notification", "sub-tabs (if any)": "", "Form fields": "Assignee Based", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Notify Subtask Assignees on Assignment/Reassignment? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Notify due tasks for the day? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Notify at (Dropdown Select - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Notify assignee on subtask deletion? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Send reminder for upcoming job? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Authority Based", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "When updated as \"any status from config\" notify (Multi-select Dropdown)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Statuswise", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Lambda", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Open..\"statuses of the config\" (Expandable Section)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable Lambda Hook (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Lambda ARN (Text Input - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Event based", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Subtask Deletion (Expandable Section)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable Lambda Hook (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Lambda ARN (Text Input - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Subtask Re-assignment (Expandable Section)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable Lambda Hook (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Lambda ARN (Text Input - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "Automations", "sub-tabs (if any)": "Basic", "Form fields": "Disallow multiple assignments at the same time? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Disallow multiple subtask creations on a request? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Disallow subtask status update before the start date? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Disable default assignee filtration by \"reporting to\"? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Show assignees by service location? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Configure Consumer OTP verification (Expandable Section)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Configure Mandatory Statuses", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Each status of config \" (Dropdown)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Configure Geo Fencing", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Geo verification limit in meters (Number Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Geo verification mode (Dropdown Select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable Geo verification for (Multi-select Dropdown)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Configure service request status movement based on subtask status (Expandable Section)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Attendance status (Select the status that will mark the start of attendance for the task) (Dropdown Select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Set attendance marking buffer (in minutes) (Number Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Assigned", "Form fields": "Lambda for auto assign (Text Input - Placeholder: Enter Lambda ARN)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Tabs Section (Informational Text)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "By Service Types (Tab Selection)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select Service Types (Multi-select Dropdown)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable service type filtration for Task based (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable unassigned filter for Task based (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable present filter for Task based (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable location group matching for Task based (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable role filter for Task based (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Info Box (Informational Text)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable nearby filter for Task based (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Max range for nearby filtration in meters (Number Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Restrictions", "Form fields": "Disallow status re-updation (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select statuses to be allowed for multiple updation (Multi-select Dropdown)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Hide statuses for updation (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "Status wise fields", "sub-tabs (if any)": "", "Form fields": "status from config\" Fields (Section Header)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Edit \"status from config\"  Fields (Link/Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Move attachments field to bottom (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Make attachments field mandatory for \"status from config\"  (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Move remarks field to bottom (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable lambda-based validation (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Is \"status from config\"  status form a dynamic form? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Column span of \"status from config\"  form fields (Default is 4 for rendering a grid) (Number Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Show audio transcripts (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select fields to transcribe (Multi-select Dropdown - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "Exceptions", "sub-tabs (if any)": "", "Form fields": "Configure Subtask Card (Expandable Section)", "after performing actions -1": "Show call consumer button on the subtask (Checkbox)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Enable calling through dialer (Checkbox)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Enable masked calling through dialer (Checkbox)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Enable alternate number calling (if present in service request) (Checkbox)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Select fields to be hidden on the subtask card (Multi-select Dropdown)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Show external order id instead of TMS request ID (Checkbox)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Dashboard Configuration (Expandable Section)", "after performing actions -1": "Select statuses to be hidden on the dashboard (only statuses in the Done bucket will be shown) (Multi-select Dropdown)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "Hide subtask on service request closure (Checkbox)", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Lock subtask details for selected statuses (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Can postpone? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "When postponing, what fields to capture (Textarea - JSON configuration)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Form preview (Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Edit postpone fields (Link)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Can reject? (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "When rejecting, what fields to capture (Textarea - JSON configuration)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Form preview (Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Edit reject fields (Link)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Start Time Slot (Lower Limit) (Time Picker)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "End Time Slot (Upper Limit) (Time Picker)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "/setup/user-config/roles", "Active Menu Tab": "User configuration", "Active sub-menu": "Roles", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Create Role (Button)", "after performing actions -3": "Select Color (Color Picker)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Name (Text Input, Required)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Description (Multiline Text Area)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Role Code (Text Input, Required)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Features Access (Multi-select Tags)", "after performing actions -4": "Manage Feature Rights (Checkbox Group)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "READ (Checkbox)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "CREATE (Checkbox)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "UPDATE (Checkbox)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Service Access (Multi-select Tags)", "after performing actions -4": "Manage Service Rights (Checkbox Group)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "READ (Checkbox)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "CREATE (Checkbox)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "UPDATE (Checkbox)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "DELETE (Checkbox)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Define sequence of subtask type listing on the dashboard (Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Onfield App access (Multi-select Dropdown)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Can add leaves for below roles (Multi-select Dropdown)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Enable auto punch out (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Can see ratings of below roles (Multi-select Dropdown)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Can see consumer phone number (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Enable call consumer button (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Enable live tracking (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Configure hidden fields (Expandable Section)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Hide due tasks widget on dashboard (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Hide TAT overview widget on dashboard (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Hide customer feedback widget on dashboard (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Move aging widget on top in dashboard (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Show assigned service requests only (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Show authorized service requests only (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "<PERSON> (Radio Button)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Is On field (Radio Button)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Is eligible for inbound billing (Radio Button)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Submit (Button)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "ALL FILTERS (Button)", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Search bar (text input)", "after performing actions -3": "gives results as per requested", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "roles created as list (click any one)", "after performing actions -3": "Select Color (Color Picker)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Name (Text Input, Required)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Description (Multiline Text Area)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Role Code (Text Input, Required)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Features Access (Multi-select Tags)", "after performing actions -4": "Manage Feature Rights (Checkbox Group)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "READ (Checkbox)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "CREATE (Checkbox)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "UPDATE (Checkbox)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Service Access (Multi-select Tags)", "after performing actions -4": "Manage Service Rights (Checkbox Group)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "READ (Checkbox)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "CREATE (Checkbox)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "UPDATE (Checkbox)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "DELETE (Checkbox)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Define sequence of subtask type listing on the dashboard (Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Onfield App access (Multi-select Dropdown)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Can add leaves for below roles (Multi-select Dropdown)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Enable auto punch out (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Can see ratings of below roles (Multi-select Dropdown)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Can see consumer phone number (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Enable call consumer button (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Enable live tracking (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Configure hidden fields (Expandable Section)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Hide due tasks widget on dashboard (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Hide TAT overview widget on dashboard (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Hide customer feedback widget on dashboard (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Move aging widget on top in dashboard (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Show assigned service requests only (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Show authorized service requests only (Checkbox)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "<PERSON> (Radio Button)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Is On field (Radio Button)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Is eligible for inbound billing (Radio Button)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Save (<PERSON>ton)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Pagination Controls (Button)", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "Locations", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Add location group (Button)", "after performing actions -3": "Group name (Required, Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Pincode (Textarea)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Cities (Autocomplete Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "State (Autocomplete Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Exclude cities (Autocomplete Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Description (Textarea)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Submit (Button)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Search bar (text input)", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "roles created as list (click any one)", "after performing actions -3": "Group name (Required, Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Pincode (Textarea)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Cities (Autocomplete Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "State (Autocomplete Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Exclude cities (Autocomplete Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Description (Textarea)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Save (<PERSON>ton)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Pagination Controls (Button)", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "Zones", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Add Zone (Button)", "after performing actions -3": "Zone name (Required, Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Select Location Group (Required, Dropdown Select)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Description (Textarea)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Submit (Button)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Search bar (text input)", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "roles created as list (click any one)", "after performing actions -3": "Zone name (Required, Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Select Location Group (Required, Dropdown Select)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Description (Textarea)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Save (<PERSON>ton)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Pagination Controls (Button)", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "User Fields", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "User custom fields (Textarea)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Open field creator (<PERSON><PERSON>)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Submit (Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "Restrictions", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Max simultaneous logins (value between 1 to 4) (Number Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Restrict user from changing password (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enable password policy (Checkbox)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Submit (Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "/setup/inventory-config/manage-custom-fields", "Active Menu Tab": "Inventory Config", "Active sub-menu": "Custom Fields", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "SKUs custom fields (Textarea - JSON input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Open field creator (<PERSON>/But<PERSON>)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Spare custom fields (Textarea - JSON input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Open field creator (<PERSON>/But<PERSON>)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Warehouse custom fields (Textarea - JSON input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Form preview (Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Open field creator (<PERSON>/But<PERSON>)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Product Stock Custom Fields (Textarea - JSON input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Open field creator (<PERSON>/But<PERSON>)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Spare Stock Custom Fields (Textarea - JSON input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Open field creator (<PERSON>/But<PERSON>)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Submit (Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "/rating/rating-templates", "Active Menu Tab": "Rating Templates", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Add a template", "after performing actions -3": "Template Name (Text Input - Required)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Enter custom fields JSON (Textarea - Required)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Open field creator (<PERSON>/But<PERSON>)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Select rating field (Dropdown)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Create a template (Button)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "List of templates added (click any one)", "after performing actions -3": "Active/inactive (toggle-<PERSON><PERSON>)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Edit (<PERSON>ton)", "after performing actions -4": "Template Name (Text Input - Required)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Enter custom fields JSON (Textarea - Required)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Open field creator (<PERSON>/But<PERSON>)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Select rating field (Dropdown)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Update template (Button)"}, {"Page": "/setup/srvc-req/settings", "Active Menu Tab": "Settings", "Active sub-menu": "Regional", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select organization timezone (Dropdown)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select consumer phone number country code (Dropdown)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select number of digits for the phone number (Number Input - Required)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Select organization country code for pincode (Dropdown)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Organization pincode length (Number Input - Read-Only)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Submit (Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "Communications", "Action": "", "Tabs": "Lambda", "sub-tabs (if any)": "", "Form fields": "Enter lambda ARN for voice calling number (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enter lambda ARN for WhatsApp communication (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Enter lambda ARN for SMS communication (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Submit (Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "SMS template", "sub-tabs (if any)": "Add SMS Template", "Form fields": "Status Type ( Dropdown - Single select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "Consumer OTP Template", "Form fields": "* Add new template value for Consumer OTP (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "* Add new template label for Consumer OTP (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "SMS Feedback Template", "Form fields": "Template Name (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "* Add new template value for Consumer Feedback (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "* Add new template label for Consumer Feedback (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "WhatsApp template", "sub-tabs (if any)": "Status Wise Template", "Form fields": "Status Type ( Dropdown - Single select)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "apply (Button)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "<PERSON><PERSON><PERSON>", "Form fields": "Template Name (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "* Add new template value for Consumer Feedback (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "* Add new template label for Consumer Feedback (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Add new template token for Consumer Feedback (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "Add new template url for Consumer Feedback (Text Input)", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "/users", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Create User (Button)", "after performing actions -3": "Click here for Bulk creation (Expandable section)", "after performing actions -4": "to be discussed"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "* Full Name (Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Code (Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Designation (Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "* Select Specific Role (Dropdown Select)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Select Location Group (Dropdown Select)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Select Reporting To (Dropdown Select)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "* Mobile Number (+91) (Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "* Email (Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Service Types (Multi-select or Dropdown Select)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Address Details (Collapsible Section)", "after performing actions -4": "Search your building/street name (Search Input with Icon Button - Text Input)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Pick on Map (Button)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Flat no (Text Input)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Building/Apartment name (Text Input)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Line 1 (Text Input - Address Line 1)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Line 2 (Text Input - Address Line 2)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Pincode (Text Input - Numeric)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "City (Text Input)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "State (Text Input)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Latitude (Disabled Text Input - Example: Eg 37.7749)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Longitude (Disabled Text Input - Example: Eg -122.4194)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "* Password (Password Input - Hidden Characters)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "* Confirm Password (Password Input - Hidden Characters)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Active (Toggle Switch)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Submit (Button)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Bulk Update (Button)", "after performing actions -3": "Download File (Button)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Upload File (Button)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "ALL FILTERS (Button/Link)", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Created users list (click on any one)", "after performing actions -3": "Full Name (Text Input - Required)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Code (Text Input - Unique identifier for the user)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Designation (Text Input - Optional)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Select Specific Role (Dropdown with multiple selections - Required)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Select Location Group (Dropdown - Optional)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Select Reporting To (Dropdown - Optional)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Mobile Number (+91) (Text Input - Required)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Email (Text Input)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Service Types (Text Input/Dropdown - Optional)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Address Details (expandable section)", "after performing actions -4": "Search your building/street name (Text Input)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Pick on Map (Button)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Flat No (Text Input )"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Building/Apartment Name (Text Input )"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Line 1 (Text Input )"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Line 2 (Text Input )"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Pincode (Text Input )"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "City (Text Input )"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "State (Text Input )"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Latitude (Text Input)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Longitude (Text Input)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Change Password (Button)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Active (Toggle Switch)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Save (<PERSON>ton)", "after performing actions -4": ""}, {"Page": "/customer", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Create Customer", "after performing actions -3": "Click here for Bulk creation (Expandable section)", "after performing actions -4": "to be discussed"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Full Name - Text (Required)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Code - Text (Required)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Email - Email (Required)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Mobile Number (+91) - Number (Required)", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Search your building/street name - Text with Search", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Flat No - Text", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Building/Apartment Name - Text", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Line 1 - Text", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Line 2 - Text", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Pincode - Dropdown/Number", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "City - Dropdown/Text", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "State - Dropdown", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "Submit - Button", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "Search bar(Text)", "after performing actions -3": "", "after performing actions -4": ""}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "created customers list (click anyone)", "after performing actions -3": "Details tab", "after performing actions -4": "Full Name - Text (Required)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Code - Text (Required)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Email - Email (Required)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Mobile Number (+91) - Number (Required)"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Search your building/street name - Text with Search"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Flat No - Text"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Building/Apartment Name - Text"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Line 1 - Text"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Line 2 - Text"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Pincode - Dropdown/Number"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "City - Dropdown/Text"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "State - Dropdown"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "", "after performing actions -4": "Save - <PERSON><PERSON>"}, {"Page": "", "Active Menu Tab": "", "Active sub-menu": "", "Action": "", "Tabs": "", "sub-tabs (if any)": "", "Form fields": "", "after performing actions -1": "", "after performing actions -2": "", "Interactive elements": "", "after performing actions -3": "History tab", "after performing actions -4": ""}]