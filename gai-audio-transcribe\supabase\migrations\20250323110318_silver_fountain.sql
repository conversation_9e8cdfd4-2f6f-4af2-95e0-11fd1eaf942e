/*
  # Create recordings table

  1. New Tables
    - `recordings`
      - `id` (uuid, primary key)
      - `user_id` (uuid, references auth.users)
      - `name` (text)
      - `audio_url` (text)
      - `transcription` (text, nullable)
      - `tone` (text, nullable)
      - `rating` (numeric, nullable)
      - `status` (text)
      - `created_at` (timestamptz)

  2. Security
    - Enable RLS on `recordings` table
    - Add policies for authenticated users to:
      - Read their own recordings
      - Create new recordings
      - Update their own recordings
*/

CREATE TABLE recordings (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users NOT NULL,
  name text NOT NULL,
  audio_url text NOT NULL,
  transcription text,
  tone text,
  rating numeric,
  status text NOT NULL DEFAULT 'processing',
  created_at timestamptz DEFAULT now()
);

ALTER TABLE recordings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can read own recordings"
  ON recordings
  FOR SELECT
  TO authenticated
  USING (auth.uid() = user_id);

CREATE POLICY "Users can create recordings"
  ON recordings
  FOR INSERT
  TO authenticated
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own recordings"
  ON recordings
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);