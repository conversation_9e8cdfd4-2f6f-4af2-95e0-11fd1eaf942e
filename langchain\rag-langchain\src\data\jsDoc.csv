<PERSON>,Active Menu Tab,Active sub-menu,Action,Tabs,sub-tabs (if any),Form fields,after performing actions -1,after performing actions -2,Interactive elements,after performing actions -3,after performing actions -4
/setup/srvc-req/types,Service Configuration,Service types,select any 1 srvc type,Basic,,Name your service (Text Input) - Max 20 characters,,,,,
,,,,,,"Service key (Text Input) - Only lowercase letters, underscores, and max 20 characters",,,,,
,,,,,,"Nature of service (Radio Buttons) - Options: ""Task based"" and ""Project based""",,,,,
,,,,,,Service description (Text Input) - Max 200 characters,,,,,
,,,,,,"Prefix for request (Text Input) - Min 3, max 4 characters",,,,,
,,,,,,Select custom field for prefix (Text Input) - Max 10 characters,,,,,
,,,,,,Include customer name in ID? (Checkbox) - Max 10 characters from the first word,,,,,
,,,,Configuration,Help,,,,,,
,,,,,Statuses,Drag & drop the statuses,,,,,
,,,,,,"delete/ add the statuses under following categories- ACTIVE, DONE, CLOSED (closed category can have only single status)",,,,,
,,,,Configuration,Fields,Enter custom fields JSON (Textarea/Input for JSON),,,,,
,,,,Configuration,Fields,Column span (Default is 2 for rendering a grid) (Number Input),,,,,
,,,,,,Rename Specific Details Label (Text Input),,,,,
,,,,,,Service category field (Dropdown Select),,,,,
,,,,,,Select alternate number fields (Dropdown Select),,,,,
,,,,,,Start Time Slot (Lower Limit) (Dropdown Select),,,,,
,,,,,,End Time Slot (Upper Limit) (Dropdown Select),,,,,
,,,,,,Are custom fields dynamic? (Checkbox),,,,,
,,,,,,Configure status update lambda (Section Header),,,,,
,,,,,,Enable lambda for status update (Checkbox),,,,,
,,,,,,Open (Text Input),,,,,
,,,,,,Label for Open (Text Input),,,,,
,,,,,,Schedule (Text Input),,,,,
,,,,,,Label for Schedule (Text Input),,,,,
,,,,,,Visit (Text Input),,,,,
,,,,,,Label for Visit (Text Input),,,,,
,,,,,,Assign (Text Input),,,,,
,,,,,,Label for Assign (Text Input),,,,,
,,,,,,Done (Text Input),,,,,
,,,,,,Label for Done (Text Input),,,,,
,,,,,,Closed (Text Input),,,,,
,,,,,,Label for Closed (Text Input),,,,,
,,,,,Service Provider,Allow requests to be assigned to service providers (Checkbox),,,,,
,,,,,,Possible service providers (Multi-select dropdown with tags),,,,,
,,,,,,Default service provider (Dropdown select),,,,,
,,,,,,Allow provider to add requests (Checkbox),,,,,
,,,,,,Auto move service request status to (Whenever onfield task is added) (Dropdown select),,,,,
,,,,,,Configure Subtask Status Movement Based on Service Request Status (Expandable section),,,,,
,,,,,,Re-use existing customer profiles while creation (Checkbox),,,,,
,,,,,,Hide consumer phone number from provider (Checkbox),,,,,
,,,,,,Hide consumer phone number from onfield users of provider (Checkbox),,,,,
,,,,,,Make custom fields non-editable from provider (Checkbox),,,,,
,,,,,Proactive,Enable Proactive (Checkbox),,,,,
,,,,,,Select source service type (Dropdown select),,,,,
,,,,,,"Facts (List of predefined data fields, displayed as tags)",,,,,
,,,,,,"Rules (Section for defining rules)- New Rule (Button),  Output (Dropdown select), Add Rule (Button)",,,,,
,,,,,Reporting,Show cumulative total on selected statuses (Checkbox),,,,,
,,,,,,Label for cumulative total (Text Input),,,,,
,,,,,,Select statuses (Multi-select dropdown),,,,,
,,,,,,Open - Select field for cumulative total (Dropdown select),,,,,
,,,,,,Enable value standardization (Checkbox),,,,,
,,,,,,Customize ageing timeframes (Checkbox),,,,,
,,,,,,Input Ageing timeframe JSON (Textarea),,,,,
,,,,,,Measure stage transition time (Aging) (Checkbox),,,,,
,,,,,,Measure TAT (open to closure) (Checkbox),,,,,
,,,,,,Measure feedback (only applicable if feedback is on) (Checkbox),,,,,
,,,,,,Measure stage-wise count (Checkbox),,,,,
,,,,,,Measure location-wise heat map (Checkbox),,,,,
,,,,,,Measure subtask vs task count (Checkbox),,,,,
,,,,,,Measure stage-wise TAT (Checkbox),,,,,
,,,,,,Measure assignees per request (Checkbox),,,,,
,,,,,,Measure number of requests per assignee (Checkbox),,,,,
,,,,,,Choose fields to be displayed on authority-wise requests (Text Input),,,,,
,,,,,Entities,Customer mobile not mandatory? (Checkbox),,,,,
,,,,,,Pin code mandatory while request creation? (Checkbox),,,,,
,,,,,,Request service date mandatory while request creation? (Checkbox),,,,,
,,,,,,Can customer rate the service? (Checkbox),,,,,
,,,,,,(An SMS will be sent to the customer to rate the service),,,,,
,,,,,,Send Feedback WhatsApp Message (Checkbox),,,,,
,,,,,,Show external order ID instead of TMS request ID (Checkbox),,,,,
,,,,,,Customer feedback form fields (Textarea/Input for JSON),,,,,
,,,,,,Preview Rate form (Button),,,,,
,,,,,,Open field creator (Button),,,,,
,,,,,,Send feedback SMS when request is moved to (Dropdown select),,,,,
,,,,,,Ratings field (Dropdown select),,,,,
,,,,,,Can users subscribe? (Checkbox),,,,,
,,,,,,Applicable sub-task type (Multi-select dropdown),,,,,
,,,,,,Configure auto status movement based on subtask creation (Expandable section),,,,,
,,,,,,Enable subtask closure on service request closure (Checkbox),,,,,
,,,,,,Authorities (Make sure that these roles have access to this service request) (Multi-select dropdown),,,,,
,,,,,,Hide authority selection from specific details (Checkbox),,,,,
,,,,,,Restrict manual authority selection during creation (Checkbox),,,,,
,,,,,,Enable cross visibility of selected authorities (Button),,,,,
,,,,,,Technician (Checkbox) --> visible only if selected in authorities multi-select,,,,,
,,,,,,Manager (Checkbox)  --> visible only if selected in authorities multi-select,,,,,
,,,,,,Technician - Show only direct reportees (Checkbox)  --> visible only if selected in authorities multi-select,,,,,
,,,,,,Manager - Show only direct reportees (Checkbox) --> visible only if selected in authorities multi-select,,,,,
,,,,,,Technician Specific Fields (only Technician of the selected Project-based can edit these fields) (Multi-select dropdown)--> visible only if selected in authorities multi-select,,,,,
,,,,,,Manager Specific Fields (only Manager of the selected Project-based can edit these fields) (Multi-select dropdown)--> visible only if selected in authorities multi-select,,,,,
,,,,,Ratings,Enable Ratings (Checkbox),,,,,
,,,,,,"Authorities (Section Header)-Here, you can configure who will rate which authority (Informational Text)",,,,,
,,,,,,Technician (Expandable Section),,,,,
,,,,,,Another Authority / Static User (Radio Button),,,,,
,,,,,,Manager (Dropdown Select),,,,,
,,,,,,Select Template (Dropdown Select),,,,,
,,,,,,Manager (Expandable Section),,,,,
,,,,,,Another Authority / Static User (Radio Button),,,,,
,,,,,,Static User (Text Input),,,,,
,,,,,,Select Template (Dropdown Select),,,,,
,,,,,,Admin (Expandable Section),,,,,
,,,,,GAI,Enable GAI ratings for subtasks(Checkbox),,,,,
,,,,,Pricing Master,Masters Rate Table (Table with Input Fields),,,,,
,,,,,,"Determination Engine Mode (radio buttons) - Lowest, highest, aggregate",,,,,
,,,,,Billing,Billing,Enable Billing (Checkbox),,,,
,,,,,,,Billing Type (Dropdown Select),,,,
,,,,,,,Categorize specific fields for billing section (Text Input),,,,
,,,,,,,Who can lock service request for billing (Multi-select Dropdown),,,,
,,,,,,,Who can sync service request price (Multi-select Dropdown),,,,
,,,,,,,Who can send a Request for billing (Multi-select Dropdown),,,,
,,,,,,,Who will get notified for billing (Multi-select Dropdown),,,,
,,,,,,Discounting,Enable Discounting (Checkbox),,,,
,,,,,,,"When discount approval status changes, notify (Multi-select Dropdown)",,,,
,,,,,,,Informational Message (Static Text),,,,
,,,,,,,Add a Rule (Button),,,,
,,,,,,,Discount Rules Table (Table with Input Fields and Actions-(edit & delete) ),,,,
,,,,,,,"Pagination Controls (Pagination Buttons: Previous, Next)",,,,
,,,,,,Additional,Enable Additional Billing (Checkbox),,,,
,,,,,,,Columns (Button),,,,
,,,,,,,Preview Table (Table with Input Fields),,,,
,,,,,Automations,Fields,Automation Movement fields (Dropdown),,,,
,,,,,,Authorities,Automation Movement Authorities (Dropdown),,,,
,,,,,,Readiness,Select subtask (multi-select),,,,
,,,,,,,Select mandatory field for Task (multi-select),,,,
,,,,,,Assignment,any role (eg.Technician)- auto assign based on location (Checkbox),,,,
,,,,,,,Apply to unassigned requests (Button),,,,
,,,,,,,Apply to all active requests (Button),,,,
,,,,,,Periodic,Add automation (Button) ,Add a periodic automation (Section Header),,,
,,,,,,,,Title (Text Input - Required),,,
,,,,,,,,Cron frequency (Text Input - Required),,,
,,,,,,,,Statuses (Multi-select Dropdown - Required),,,
,,,,,,,,Lambda ARN (Text Input - Required),,,
,,,,,,,,Add (Button),,,
,,,,,,Subtask,Configure Subtask Status Movement Based on Service Request Status (Expandable Section),,,,
,,,,,,,Enable? (Checkbox),,,,
,,,,,,,Service Type Statuses (Multi-select Dropdown),,,,
,,,,,,,Subtask Status Mapping Table (Table with Dropdown Inputs),,,,
,,,,,,,"Pagination Controls (Pagination Buttons: Previous, Next)",,,,
,,,,,Tabular view,Select table columns (Multi-select),,,,,
,,,,,Views,Select Brand Filters (Multi-select),,,,,
,,,,Notification,Status wise,Consumer,select one of the status (Expandable section),,,,
,,,,,,,SMS / WhatsApp (Tab Selection),,,,
,,,,,,,Enable (Checkbox),,,,
,,,,,,,Status Type (Dropdown Select),,,,
,,,,,,,Template (Dropdown Select),,,,
,,,,,,Authorities,"When updated as ""any status from config"" notify (multi-select)",,,,
,,,,,Field wise,Authority (tab),Add a Notification (button),,,,
,,,,,,,Select specific field (Dropdown Select),,,,
,,,,,,,Select authorities (Dropdown Select),,,,
,,,,,,,Notification added will be visible in tabular format,,,,
,,,,,Event based,Service Request Deletion (Expandable Section),,,,,
,,,,,,Enable Lambda Hook (Checkbox),,,,,
,,,,,,Lambda ARN (Text Input - Required),,,,,
,,,,,Time based,Add a Notification (button),,,,,
,,,,,,Add a time-based notification rule (Section Header),,,,,
,,,,,,Notification Title (Text Input - Required),,,,,
,,,,,,Notification Start Date (Date Picker - Required),,,,,
,,,,,,Repeat Every (Number Input - Required),,,,,
,,,,,,Repeat Interval (Dropdown Select - Required),,,,,
,,,,,,"Options: Day, Week, Month, etc.",,,,,
,,,,,,Notification will trigger at 9:00 AM (Informational Text),,,,,
,,,,,,Select Primary Recipient (Text Input - Required),,,,,
,,,,,,Static CC Users (Multi-select Dropdown),,,,,
,,,,,,Select status for ageing report notification (Dropdown Select - Required),,,,,
,,,,,,Lambda ARN for ageing report notification (Text Input - Required),,,,,
,,,,,,Notification added will be visible in tabular format with edit/delete functionality,,,,,
/setup/srvc-req/sub-tasks-types,Service Configuration,Service types,select any 1 Subtask type,Basic,,"Name your subtask type (Text Input - Required, Max 20 characters)",,,,,
,,,,,,"Subtask key (Text Input - Required, Only lowercase letters, underscores, Max 20 characters)",,,,,
,,,,,,Onfield task? (Checkbox),,,,,
,,,,,,"(If this task is an onfield task, then check the above box)",,,,,
,,,,,,"Subtask description (Textarea - Required, Max 200 characters)",,,,,
,,,,,,Icon (Dropdown Select - Required),,,,,
,,,,Statuses,,Drag & drop the statuses,,,,,
,,,,,,"delete/ add the statuses under following categories- ACTIVE, DONE, CLOSED (closed category can have only single status)",,,,,
,,,,Notification,,Assignee Based,,,,,
,,,,,,Notify Subtask Assignees on Assignment/Reassignment? (Checkbox),,,,,
,,,,,,Notify due tasks for the day? (Checkbox),,,,,
,,,,,,Notify at (Dropdown Select - Required),,,,,
,,,,,,Notify assignee on subtask deletion? (Checkbox),,,,,
,,,,,,Send reminder for upcoming job? (Checkbox),,,,,
,,,,,,Authority Based,,,,,
,,,,,,"When updated as ""any status from config"" notify (Multi-select Dropdown)",,,,,
,,,,,,Statuswise ,,,,,
,,,,,,Lambda ,,,,,
,,,,,,"Open..""statuses of the config"" (Expandable Section)",,,,,
,,,,,,Enable Lambda Hook (Checkbox),,,,,
,,,,,,Lambda ARN (Text Input - Required),,,,,
,,,,,,Event based,,,,,
,,,,,,Subtask Deletion (Expandable Section),,,,,
,,,,,,Enable Lambda Hook (Checkbox),,,,,
,,,,,,Lambda ARN (Text Input - Required),,,,,
,,,,,,Subtask Re-assignment (Expandable Section),,,,,
,,,,,,Enable Lambda Hook (Checkbox),,,,,
,,,,,,Lambda ARN (Text Input - Required),,,,,
,,,,Automations,Basic,Disallow multiple assignments at the same time? (Checkbox),,,,,
,,,,,,Disallow multiple subtask creations on a request? (Checkbox),,,,,
,,,,,,Disallow subtask status update before the start date? (Checkbox),,,,,
,,,,,,"Disable default assignee filtration by ""reporting to""? (Checkbox)",,,,,
,,,,,,Show assignees by service location? (Checkbox),,,,,
,,,,,,Configure Consumer OTP verification (Expandable Section),,,,,
,,,,,,Configure Mandatory Statuses,,,,,
,,,,,,"""Each status of config "" (Dropdown)",,,,,
,,,,,,Configure Geo Fencing,,,,,
,,,,,,Geo verification limit in meters (Number Input),,,,,
,,,,,,Geo verification mode (Dropdown Select),,,,,
,,,,,,Enable Geo verification for (Multi-select Dropdown),,,,,
,,,,,,Configure service request status movement based on subtask status (Expandable Section),,,,,
,,,,,,Attendance status (Select the status that will mark the start of attendance for the task) (Dropdown Select),,,,,
,,,,,,Set attendance marking buffer (in minutes) (Number Input),,,,,
,,,,,Assigned,Lambda for auto assign (Text Input - Placeholder: Enter Lambda ARN),,,,,
,,,,,,Tabs Section (Informational Text),,,,,
,,,,,,By Service Types (Tab Selection),,,,,
,,,,,,Select Service Types (Multi-select Dropdown),,,,,
,,,,,,Enable service type filtration for Task based (Checkbox),,,,,
,,,,,,Enable unassigned filter for Task based (Checkbox),,,,,
,,,,,,Enable present filter for Task based (Checkbox),,,,,
,,,,,,Enable location group matching for Task based (Checkbox),,,,,
,,,,,,Enable role filter for Task based (Checkbox),,,,,
,,,,,,Info Box (Informational Text),,,,,
,,,,,,Enable nearby filter for Task based (Checkbox),,,,,
,,,,,,Max range for nearby filtration in meters (Number Input),,,,,
,,,,,Restrictions ,Disallow status re-updation (Checkbox),,,,,
,,,,,,Select statuses to be allowed for multiple updation (Multi-select Dropdown),,,,,
,,,,,,Hide statuses for updation (Checkbox),,,,,
,,,,Status wise fields,,"""status from config"" Fields (Section Header)",,,,,
,,,,,,"Edit ""status from config""  Fields (Link/Button)",,,,,
,,,,,,Move attachments field to bottom (Checkbox),,,,,
,,,,,,"Make attachments field mandatory for ""status from config""  (Checkbox)",,,,,
,,,,,,Move remarks field to bottom (Checkbox),,,,,
,,,,,,Enable lambda-based validation (Checkbox),,,,,
,,,,,,"Is ""status from config""  status form a dynamic form? (Checkbox)",,,,,
,,,,,,"Column span of ""status from config""  form fields (Default is 4 for rendering a grid) (Number Input)",,,,,
,,,,,,Show audio transcripts (Checkbox),,,,,
,,,,,,Select fields to transcribe (Multi-select Dropdown - Required),,,,,
,,,,Exceptions,,Configure Subtask Card (Expandable Section),Show call consumer button on the subtask (Checkbox),,,,
,,,,,,,Enable calling through dialer (Checkbox),,,,
,,,,,,,Enable masked calling through dialer (Checkbox),,,,
,,,,,,,Enable alternate number calling (if present in service request) (Checkbox),,,,
,,,,,,,Select fields to be hidden on the subtask card (Multi-select Dropdown),,,,
,,,,,,,Show external order id instead of TMS request ID (Checkbox),,,,
,,,,,,Dashboard Configuration (Expandable Section),Select statuses to be hidden on the dashboard (only statuses in the Done bucket will be shown) (Multi-select Dropdown),,,,
,,,,,,,Hide subtask on service request closure (Checkbox),,,,
,,,,,,Lock subtask details for selected statuses (Checkbox),,,,,
,,,,,,Can postpone? (Checkbox),,,,,
,,,,,,"When postponing, what fields to capture (Textarea - JSON configuration)",,,,,
,,,,,,Form preview (Button),,,,,
,,,,,,Edit postpone fields (Link),,,,,
,,,,,,Can reject? (Checkbox),,,,,
,,,,,,"When rejecting, what fields to capture (Textarea - JSON configuration)",,,,,
,,,,,,Form preview (Button),,,,,
,,,,,,Edit reject fields (Link),,,,,
,,,,,,Start Time Slot (Lower Limit) (Time Picker),,,,,
,,,,,,End Time Slot (Upper Limit) (Time Picker),,,,,
/setup/user-config/roles,User configuration,Roles,,,,,,,Create Role (Button),"Select Color (Color Picker)
",
,,,,,,,,,,"Name (Text Input, Required)",
,,,,,,,,,,Description (Multiline Text Area),
,,,,,,,,,,"Role Code (Text Input, Required)",
,,,,,,,,,,Features Access (Multi-select Tags),Manage Feature Rights (Checkbox Group)
,,,,,,,,,,,READ (Checkbox)
,,,,,,,,,,,CREATE (Checkbox)
,,,,,,,,,,,UPDATE (Checkbox)
,,,,,,,,,,Service Access (Multi-select Tags),Manage Service Rights (Checkbox Group)
,,,,,,,,,,,READ (Checkbox)
,,,,,,,,,,,CREATE (Checkbox)
,,,,,,,,,,,UPDATE (Checkbox)
,,,,,,,,,,,DELETE (Checkbox)
,,,,,,,,,,Define sequence of subtask type listing on the dashboard (Text Input),
,,,,,,,,,,Onfield App access (Multi-select Dropdown),
,,,,,,,,,,Can add leaves for below roles (Multi-select Dropdown),
,,,,,,,,,,Enable auto punch out (Checkbox),
,,,,,,,,,,Can see ratings of below roles (Multi-select Dropdown),
,,,,,,,,,,Can see consumer phone number (Checkbox),
,,,,,,,,,,Enable call consumer button (Checkbox),
,,,,,,,,,,Enable live tracking (Checkbox),
,,,,,,,,,,Configure hidden fields (Expandable Section),
,,,,,,,,,,Hide due tasks widget on dashboard (Checkbox),
,,,,,,,,,,Hide TAT overview widget on dashboard (Checkbox),
,,,,,,,,,,Hide customer feedback widget on dashboard (Checkbox),
,,,,,,,,,,Move aging widget on top in dashboard (Checkbox),
,,,,,,,,,,Show assigned service requests only (Checkbox),
,,,,,,,,,,Show authorized service requests only (Checkbox),
,,,,,,,,,,Is Admin (Radio Button),
,,,,,,,,,,Is On field (Radio Button),
,,,,,,,,,,Is eligible for inbound billing (Radio Button),
,,,,,,,,,,Submit (Button),
,,,,,,,,,ALL FILTERS (Button),,
,,,,,,,,,Search bar (text input),gives results as per requested ,
,,,,,,,,,roles created as list (click any one),"Select Color (Color Picker)
",
,,,,,,,,,,"Name (Text Input, Required)",
,,,,,,,,,,Description (Multiline Text Area),
,,,,,,,,,,"Role Code (Text Input, Required)",
,,,,,,,,,,Features Access (Multi-select Tags),Manage Feature Rights (Checkbox Group)
,,,,,,,,,,,READ (Checkbox)
,,,,,,,,,,,CREATE (Checkbox)
,,,,,,,,,,,UPDATE (Checkbox)
,,,,,,,,,,Service Access (Multi-select Tags),Manage Service Rights (Checkbox Group)
,,,,,,,,,,,READ (Checkbox)
,,,,,,,,,,,CREATE (Checkbox)
,,,,,,,,,,,UPDATE (Checkbox)
,,,,,,,,,,,DELETE (Checkbox)
,,,,,,,,,,Define sequence of subtask type listing on the dashboard (Text Input),
,,,,,,,,,,Onfield App access (Multi-select Dropdown),
,,,,,,,,,,Can add leaves for below roles (Multi-select Dropdown),
,,,,,,,,,,Enable auto punch out (Checkbox),
,,,,,,,,,,Can see ratings of below roles (Multi-select Dropdown),
,,,,,,,,,,Can see consumer phone number (Checkbox),
,,,,,,,,,,Enable call consumer button (Checkbox),
,,,,,,,,,,Enable live tracking (Checkbox),
,,,,,,,,,,Configure hidden fields (Expandable Section),
,,,,,,,,,,Hide due tasks widget on dashboard (Checkbox),
,,,,,,,,,,Hide TAT overview widget on dashboard (Checkbox),
,,,,,,,,,,Hide customer feedback widget on dashboard (Checkbox),
,,,,,,,,,,Move aging widget on top in dashboard (Checkbox),
,,,,,,,,,,Show assigned service requests only (Checkbox),
,,,,,,,,,,Show authorized service requests only (Checkbox),
,,,,,,,,,,Is Admin (Radio Button),
,,,,,,,,,,Is On field (Radio Button),
,,,,,,,,,,Is eligible for inbound billing (Radio Button),
,,,,,,,,,,Save (Button),
,,,,,,,,,Pagination Controls (Button),,
,,Locations,,,,,,,Add location group (Button),"Group name (Required, Text Input)",
,,,,,,,,,,Pincode (Textarea),
,,,,,,,,,,Cities (Autocomplete Text Input),
,,,,,,,,,,State (Autocomplete Text Input),
,,,,,,,,,,Exclude cities (Autocomplete Text Input),
,,,,,,,,,,Description (Textarea),
,,,,,,,,,,Submit (Button),
,,,,,,,,,Search bar (text input),,
,,,,,,,,,roles created as list (click any one),"Group name (Required, Text Input)",
,,,,,,,,,,Pincode (Textarea),
,,,,,,,,,,Cities (Autocomplete Text Input),
,,,,,,,,,,State (Autocomplete Text Input),
,,,,,,,,,,Exclude cities (Autocomplete Text Input),
,,,,,,,,,,Description (Textarea),
,,,,,,,,,,Save (Button),
,,,,,,,,,Pagination Controls (Button),,
,,Zones,,,,,,,Add Zone (Button),"Zone name (Required, Text Input)",
,,,,,,,,,,"Select Location Group (Required, Dropdown Select)",
,,,,,,,,,,Description (Textarea),
,,,,,,,,,,Submit (Button),
,,,,,,,,,Search bar (text input),,
,,,,,,,,,roles created as list (click any one),"Zone name (Required, Text Input)",
,,,,,,,,,,"Select Location Group (Required, Dropdown Select)",
,,,,,,,,,,Description (Textarea),
,,,,,,,,,,Save (Button),
,,,,,,,,,Pagination Controls (Button),,
,,User Fields,,,,User custom fields (Textarea),,,,,
,,,,,,Open field creator (Button),,,,,
,,,,,,Submit (Button),,,,,
,,Restrictions ,,,,Max simultaneous logins (value between 1 to 4) (Number Input),,,,,
,,,,,,Restrict user from changing password (Checkbox),,,,,
,,,,,,Enable password policy (Checkbox),,,,,
,,,,,,Submit (Button),,,,,
/setup/inventory-config/manage-custom-fields,Inventory Config,Custom Fields,,,,SKUs custom fields (Textarea - JSON input),,,,,
,,,,,,Open field creator (Link/Button),,,,,
,,,,,,Spare custom fields (Textarea - JSON input),,,,,
,,,,,,Open field creator (Link/Button),,,,,
,,,,,,Warehouse custom fields (Textarea - JSON input),,,,,
,,,,,,Form preview (Button),,,,,
,,,,,,Open field creator (Link/Button),,,,,
,,,,,,Product Stock Custom Fields (Textarea - JSON input),,,,,
,,,,,,Open field creator (Link/Button),,,,,
,,,,,,Spare Stock Custom Fields (Textarea - JSON input),,,,,
,,,,,,Open field creator (Link/Button),,,,,
,,,,,,Submit (Button),,,,,
/rating/rating-templates,Rating Templates ,,,,,,,,Add a template ,Template Name (Text Input - Required),
,,,,,,,,,,Enter custom fields JSON (Textarea - Required),
,,,,,,,,,,Open field creator (Link/Button),
,,,,,,,,,,Select rating field (Dropdown),
,,,,,,,,,,Create a template (Button),
,,,,,,,,,List of templates added (click any one),Active/inactive (toggle-Button),
,,,,,,,,,,Edit (Button),Template Name (Text Input - Required)
,,,,,,,,,,,Enter custom fields JSON (Textarea - Required)
,,,,,,,,,,,Open field creator (Link/Button)
,,,,,,,,,,,Select rating field (Dropdown)
,,,,,,,,,,,Update template (Button)
/setup/srvc-req/settings,Settings,Regional,,,,Select organization timezone (Dropdown),,,,,
,,,,,,Select consumer phone number country code (Dropdown),,,,,
,,,,,,Select number of digits for the phone number (Number Input - Required),,,,,
,,,,,,Select organization country code for pincode (Dropdown),,,,,
,,,,,,Organization pincode length (Number Input - Read-Only),,,,,
,,,,,,Submit (Button),,,,,
,,Communications,,Lambda,,Enter lambda ARN for voice calling number (Text Input),,,,,
,,,,,,Enter lambda ARN for WhatsApp communication (Text Input),,,,,
,,,,,,Enter lambda ARN for SMS communication (Text Input),,,,,
,,,,,,Submit (Button),,,,,
,,,,SMS template,Add SMS Template,Status Type ( Dropdown - Single select),,,,,
,,,,,Consumer OTP Template ,* Add new template value for Consumer OTP (Text Input),,,,,
,,,,,,* Add new template label for Consumer OTP (Text Input),,,,,
,,,,,SMS Feedback Template,Template Name (Text Input),,,,,
,,,,,,* Add new template value for Consumer Feedback (Text Input),,,,,
,,,,,,* Add new template label for Consumer Feedback (Text Input),,,,,
,,,,WhatsApp template ,Status Wise Template,Status Type ( Dropdown - Single select),,,,,
,,,,,,apply (Button),,,,,
,,,,,Feedback Template,Template Name (Text Input),,,,,
,,,,,,* Add new template value for Consumer Feedback (Text Input),,,,,
,,,,,,* Add new template label for Consumer Feedback (Text Input),,,,,
,,,,,,Add new template token for Consumer Feedback (Text Input),,,,,
,,,,,,Add new template url for Consumer Feedback (Text Input),,,,,
/users,,,,,,,,,Create User (Button),Click here for Bulk creation (Expandable section),to be discussed
,,,,,,,,,,* Full Name (Text Input),
,,,,,,,,,,Code (Text Input),
,,,,,,,,,,Designation (Text Input),
,,,,,,,,,,* Select Specific Role (Dropdown Select),
,,,,,,,,,,Select Location Group (Dropdown Select),
,,,,,,,,,,Select Reporting To (Dropdown Select),
,,,,,,,,,,* Mobile Number (+91) (Text Input),
,,,,,,,,,,* Email (Text Input),
,,,,,,,,,,Service Types (Multi-select or Dropdown Select),
,,,,,,,,,,Address Details (Collapsible Section),Search your building/street name (Search Input with Icon Button - Text Input)
,,,,,,,,,,,Pick on Map (Button)
,,,,,,,,,,,Flat no (Text Input)
,,,,,,,,,,,Building/Apartment name (Text Input)
,,,,,,,,,,,Line 1 (Text Input - Address Line 1)
,,,,,,,,,,,Line 2 (Text Input - Address Line 2)
,,,,,,,,,,,Pincode (Text Input - Numeric)
,,,,,,,,,,,City (Text Input)
,,,,,,,,,,,State (Text Input)
,,,,,,,,,,,Latitude (Disabled Text Input - Example: Eg 37.7749)
,,,,,,,,,,,Longitude (Disabled Text Input - Example: Eg -122.4194)
,,,,,,,,,,* Password (Password Input - Hidden Characters),
,,,,,,,,,,* Confirm Password (Password Input - Hidden Characters),
,,,,,,,,,,Active (Toggle Switch),
,,,,,,,,,,Submit (Button),
,,,,,,,,,Bulk Update (Button),Download File (Button),
,,,,,,,,,,Upload File (Button),
,,,,,,,,,ALL FILTERS (Button/Link),,
,,,,,,,,,Created users list (click on any one),Full Name (Text Input - Required),
,,,,,,,,,,Code (Text Input - Unique identifier for the user),
,,,,,,,,,,Designation (Text Input - Optional),
,,,,,,,,,,Select Specific Role (Dropdown with multiple selections - Required),
,,,,,,,,,,Select Location Group (Dropdown - Optional),
,,,,,,,,,,Select Reporting To (Dropdown - Optional),
,,,,,,,,,,Mobile Number (+91) (Text Input - Required),
,,,,,,,,,,Email (Text Input),
,,,,,,,,,,Service Types (Text Input/Dropdown - Optional),
,,,,,,,,,,Address Details (expandable section),Search your building/street name (Text Input)
,,,,,,,,,,,Pick on Map (Button)
,,,,,,,,,,,Flat No (Text Input )
,,,,,,,,,,,Building/Apartment Name (Text Input )
,,,,,,,,,,,Line 1 (Text Input )
,,,,,,,,,,,Line 2 (Text Input )
,,,,,,,,,,,Pincode (Text Input )
,,,,,,,,,,,City (Text Input )
,,,,,,,,,,,State (Text Input )
,,,,,,,,,,,Latitude (Text Input)
,,,,,,,,,,,Longitude (Text Input)
,,,,,,,,,,Change Password (Button),
,,,,,,,,,,Active (Toggle Switch),
,,,,,,,,,,Save (Button),
/customer,,,,,,,,,Create Customer,Click here for Bulk creation (Expandable section),to be discussed
,,,,,,,,,,Full Name - Text (Required),
,,,,,,,,,,Code - Text (Required),
,,,,,,,,,,Email - Email (Required),
,,,,,,,,,,Mobile Number (+91) - Number (Required),
,,,,,,,,,,Search your building/street name - Text with Search,
,,,,,,,,,,Flat No - Text,
,,,,,,,,,,Building/Apartment Name - Text,
,,,,,,,,,,Line 1 - Text,
,,,,,,,,,,Line 2 - Text,
,,,,,,,,,,Pincode - Dropdown/Number,
,,,,,,,,,,City - Dropdown/Text,
,,,,,,,,,,State - Dropdown,
,,,,,,,,,,Submit - Button,
,,,,,,,,,Search bar(Text),,
,,,,,,,,,created customers list (click anyone),Details tab,Full Name - Text (Required)
,,,,,,,,,,,Code - Text (Required)
,,,,,,,,,,,Email - Email (Required)
,,,,,,,,,,,Mobile Number (+91) - Number (Required)
,,,,,,,,,,,Search your building/street name - Text with Search
,,,,,,,,,,,Flat No - Text
,,,,,,,,,,,Building/Apartment Name - Text
,,,,,,,,,,,Line 1 - Text
,,,,,,,,,,,Line 2 - Text
,,,,,,,,,,,Pincode - Dropdown/Number
,,,,,,,,,,,City - Dropdown/Text
,,,,,,,,,,,State - Dropdown
,,,,,,,,,,,Save - Button
,,,,,,,,,,History tab,