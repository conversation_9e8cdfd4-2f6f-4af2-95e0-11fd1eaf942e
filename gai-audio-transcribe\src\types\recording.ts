export interface Recording {
  id: string;
  name: string;
  audioUrl: string;
  timestamp: Date;
  transcription: string | null;
  tone: string | null;
  rating: number | null;
  status: 'processing' | 'completed' | 'error';
  summary?: string | null;
  call_evaluation?: CallEvaluation;
}

export interface CallEvaluation {
  call_greetings: EvaluationScore;
  company_name: EvaluationScore;
  calling_reason: EvaluationScore;
  soft_skills: EvaluationScore;
  service_time: EvaluationScore;
  customer_questions_handled: EvaluationScore;
  language_proficiency: EvaluationScore;
  clarity_of_communication: EvaluationScore;
}

export interface EvaluationScore {
  score: number;
  comment: string;
}

export interface Rating {
  value: number;
  description: string;
}