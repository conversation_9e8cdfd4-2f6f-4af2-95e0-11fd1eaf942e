{"personalInformation": {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "phone": "9136132194", "location": "Himachal Pradesh", "github": "bndu2002/my-backend (github.com)"}, "workExperience": [{"company": "Installco WIFY Technology Pvt Ltd.", "title": "<PERSON> Web Developer", "startDate": "11/2023", "endDate": "Present"}, {"company": "Installco WIFY Technology Pvt Ltd.", "title": "<PERSON><PERSON>, Web Developer", "startDate": "09/2023", "endDate": "11/2023"}, {"company": "Function Up", "title": "Backend Development Trainee", "startDate": "07/2022", "endDate": "11/2022"}, {"company": "Function Up", "title": "Teaching Assistant", "startDate": "08/2022", "endDate": "12/2022"}, {"company": "Function Up", "title": "Campus Ambassador", "startDate": "03/2022", "endDate": "07/2022"}], "education": [{"institution": "<PERSON><PERSON> College, DU", "degree": "BA English Honours", "startDate": "07/2019", "endDate": "07/2022"}, {"institution": "SSRSK Vidyalaya, New Delhi", "degree": "12th", "date": "05/2019"}, {"institution": "SSRSK Vidyalaya, New Delhi", "degree": "10th", "date": "06/2017"}], "projects": [{"name": "Technician Onboarding, Installco WIFY Technology Pvt Ltd.", "description": "Live project that is used by 3000+ users to work as a technician. This includes the entire user journey from authentication to updating profile, uploading documents, taking tests and finally getting interviewed.", "techStack": ["NodeJS", "TypeScript", "Prisma", "GrapphQl", "AWS S3", "ReactJS", "Chakra UI"]}, {"name": "Admin Panel, Installco WIFY Technology Pvt Ltd.", "description": "Live project used by admins to manage 7000+ users with their documents verfication, test creation and assignment, interview scheduling, exporting user details.", "techStack": ["NodeJS", "Prisma", "GrapphQl", "TypeScript", "AWS S3", "ReactJS", "AntD"]}, {"name": "QR Code Scanner & Generator", "techStack": ["JavaScript", "NodeJS", "React", "React QR-Scanner"]}, {"name": "G-Drive Risk Report", "techStack": ["MERN", "Ant Design", "Google Developer apis", "React router"]}, {"name": "E-Commerce Shopping Cart", "techStack": ["JavaScript", "NodeJS", "MongoDB", "AWS S3"]}, {"name": "URL Shortener", "techStack": ["JavaScript", "NodeJS", "MongoDB", "Redis"]}, {"name": "Book Management", "techStack": ["JavaScript", "NodeJS", "MongoDB", "AWS S3"]}], "skills": ["JavaScript", "Nodej<PERSON>", "DBMS (PostgreSQL, Mongo DB)", "Git", "AWS S3", "Prisma", "GraphQl", "Redis", "Html", "CSS", "Tailwind", "Zustand", "Capacitor JS", "React", "DSA (Basics)", "Postman"]}