// Follow these steps to enable this Edge Function:
// 1. Go to the Supabase Dashboard
// 2. Click on Edge Functions in the sidebar
// 3. Click on "New Function"
// 4. Copy this code into the editor
// 5. Deploy the function

import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    const { recordingId, audioUrl } = await req.json()

    // Create a Supabase client with the Auth context of the logged in user
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: { headers: { Authorization: req.headers.get('Authorization')! } },
      }
    )

    // Simulate processing delay
    await new Promise(resolve => setTimeout(resolve, 2000))

    // Simulate success rate
    const success = Math.random() > 0.1

    if (success) {
      // Update recording with transcription results
      const { error: updateError } = await supabaseClient
        .from('recordings')
        .update({
          transcription: 'This is a simulated transcription from the edge function.',
          tone: 'Professional',
          rating: 4.5,
          status: 'completed'
        })
        .eq('id', recordingId)

      if (updateError) throw updateError

      return new Response(
        JSON.stringify({ message: 'Recording processed successfully' }),
        {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          status: 200,
        }
      )
    } else {
      // Update recording status to error
      const { error: updateError } = await supabaseClient
        .from('recordings')
        .update({
          status: 'error'
        })
        .eq('id', recordingId)

      if (updateError) throw updateError

      throw new Error('Processing failed')
    }
  } catch (error) {
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})