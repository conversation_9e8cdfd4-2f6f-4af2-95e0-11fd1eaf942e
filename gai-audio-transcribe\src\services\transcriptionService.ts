import axios from "axios";
import { supabase } from "../lib/supabase";
import type { Recording, CallEvaluation } from "../types/recording";

interface AnalysisResult {
  complete_conversation: string;
  tone_of_the_conversation: string;
  rating: {
    value: number;
    description: string;
  };
  call_evaluation: CallEvaluation;
  summary: string;
}

interface AudioParams {
  fileUrl: string;
  mimeType: string;
  file: File | Blob;
}

const systemInstructionPrompt = `Please evaluate the transcript of a call between a Wify technician and a customer based on the following criteria and provide the output in the specified JSON format:

1. Call Greetings (5%): Did the executive begin the call with a proper greeting, such as "Good morning, this is [Name] from [Company Name]"?

2. Company Name Provided (5%): Did the executive mention the company name during the introduction or conversation?

3. Calling Reason Provided (5%): Was the purpose of the call clearly explained to the customer?

4. Soft Skills (20%): Assess the executive's politeness, empathy, patience, and overall tone during the conversation.

5. Service Time Mentioned (10%): Did the executive provide a specific timeline for service delivery or resolution?

6. Customer Questions Handled (10% - Optional): If the customer asked questions or raised concerns, did the executive effectively address them? (Include this if applicable.)

7. Language Proficiency (10%): Rate the executive's fluency and correctness in the language used (whether Hindi or English).

8. Clarity of Communication (20%): Evaluate the clarity, articulation, and pacing of the executive's speech during the call.

After evaluating these parameters, provide the output in the following JSON format:

{

  "complete_conversation": "<transcript the call conversation between the customer and wify technician where wify technician has done the job for the customer . give me proper transcript with speaker identification and format the conversation between the customer and technician as follows:
- Each message should be preceded by the speaker's role, e.g., **Customer:** or **Wify Technician:**.
- Ensure that each speaker's message appears on a new line, with appropriate line breaks between each exchange.
- Do not skip any messages and ensure that the conversation flows smoothly.,

  "tone_of_the_conversation": "<Calm | Rude | Professional | Courteous | Polite | Angry> - <description of the tone>",
  "summary" : "<summary based on complete_conversation>"
  "rating": {
    "value": <out of 100>,
    "description": "<rating description>"
  },
  "call_evaluation": {
    "call_greetings": {
      "score": <0-5>,
      "comment": "<comment on greetings>"
    },
    "company_name": {
      "score": <0-5>,
      "comment": "<comment on company name>"
    },
    "calling_reason": {
      "score": <0-5>,
      "comment": "<comment on calling reason>"
    },
    "soft_skills": {
      "score": <0-20>,
      "comment": "<comment on soft skills>"
    },
    "service_time": {
      "score": <0-10>,
      "comment": "<comment on service time>"
    },
    "customer_questions_handled": {
      "score": <0-10>,
      "comment": "<comment on handling questions>"
    },
    "language_proficiency": {
      "score": <0-10>,
      "comment": "<comment on language proficiency>"
    },
    "clarity_of_communication": {
      "score": <0-20>,
      "comment": "<comment on clarity of communication>"
    }
  }
}`;

const analyseAudio = async ({
  file,
  fileUrl,
  mimeType,
}: AudioParams): Promise<AnalysisResult | null> => {
  try {
    //Check the language of audio
    const identifyLanguage = await geminiSpeechToTextApi({
      file,
      fileUrl,
      mimeType,
      jsonFormat: true,
      systemInstruction: `you need to answer in true or false. if given text is in english give true else false. return in json format with following formate - \n
        {
          "is_english": <true or false>
          "language_code": <language code of the text, eg: "hi-IN" , make shure to add the country code too eg : "IN">
        }`,
    });

    //probability of technician call being completely in english is very low
    const language = identifyLanguage?.data;

    console.log("transcriptionService :: analyseAudio :: language", language);

    let convoData;

    if (language?.is_english) {
      convoData = await geminiSpeechToTextApi({
        file,
        fileUrl,
        mimeType,
        jsonFormat: true,
        systemInstruction: systemInstructionPrompt,
      });
    } else {
      const translationResponse = await sarvamApi({
        fileUrl,
        mimeType,
        jsonFormat: true,
        languageCode: language?.language_code || "hi-IN",
        file:
          file instanceof File
            ? file
            : new File([file], "audio.wav", { type: mimeType }),
      });

      if (!translationResponse?.data?.transcript) {
        console.error("Unable to translate voice transcription");
        return null;
      }

      convoData = await geminiTextGeneartionApi({
        userQuery: translationResponse?.data?.transcript?.transcript,
        systemInstruction: systemInstructionPrompt,
        jsonFormat: true,
      });
    }

    console.log(
      "transcriptionService :: analyse audio :: convoData",
      convoData
    );

    if (!convoData?.data) {
      return {
        complete_conversation: "Transcription failed",
        tone_of_the_conversation: "NA",
        rating: {
          value: 0,
          description: "NA",
        },
        call_evaluation: {
          call_greetings: { score: 0, comment: "NA" },
          company_name: { score: 0, comment: "NA" },
          calling_reason: { score: 0, comment: "NA" },
          soft_skills: { score: 0, comment: "NA" },
          service_time: { score: 0, comment: "NA" },
          customer_questions_handled: { score: 0, comment: "NA" },
          language_proficiency: { score: 0, comment: "NA" },
          clarity_of_communication: { score: 0, comment: "NA" },
        },
        summary: "",
      };
    }

    return convoData?.data;
  } catch (error) {
    console.error("Error analyzing audio:", error);
    return null;
  }
};

export const transcribeAudio = async (
  file: File | Blob,
  audioUrl: string,
  recordingId: string,
  recordingName: string,
  mimeType: string
): Promise<Recording | null> => {
  try {
    const analysisResult = await analyseAudio({
      file,
      fileUrl: audioUrl,
      mimeType,
    });

    if (!analysisResult) {
      const { data: errorRecording, error } = await supabase
        .from("recordings")
        .update({
          status: "error",
        })
        .eq("id", recordingId)
        .select()
        .single();

      if (error) throw error;
      return errorRecording;
    }

    console.log(
      "transcriptionService :: transcribeAudio :: analysisResult",
      analysisResult
    );

    const { data: updatedRecording, error } = await supabase
      .from("recordings")
      .update({
        transcription: analysisResult?.complete_conversation,
        tone: analysisResult?.tone_of_the_conversation,
        rating: analysisResult?.rating?.value / 20, // Convert to 5-star scale
        status: "completed",
        call_evaluation: analysisResult?.call_evaluation,
        summary: analysisResult?.summary,
      })
      .eq("id", recordingId)
      .select()
      .single();

    if (error) throw error;
    return updatedRecording;
  } catch (error) {
    console.error("Error in transcribeAudio:", error);
    const { data: errorRecording } = await supabase
      .from("recordings")
      .update({
        status: "error",
      })
      .eq("id", recordingId)
      .select()
      .single();

    return errorRecording;
  }
};

const geminiSpeechToTextApi = async ({
  file,
  fileUrl,
  mimeType,
  systemInstruction,
  jsonFormat,
}: {
  file: File | Blob;
  fileUrl: string;
  mimeType: string;
  systemInstruction: string;
  jsonFormat: boolean;
}) => {
  try {
    const formData = new FormData();
    const jsonData = {
      fileUrl: fileUrl,
      mimeType: mimeType,
      jsonFormat: jsonFormat,
      prompt: systemInstruction,
    };
    formData.append("file", file);
    formData.append("data", JSON.stringify(jsonData));

    if (!import.meta.env.VITE_BACKEND_URL) {
      throw new Error("Backend URL not configured");
    }

    if (!import.meta.env.VITE_X_API_KEY) {
      throw new Error("API key not configured");
    }

    const response = await axios.post(
      `${import.meta.env.VITE_BACKEND_URL}/gemini/speech-to-text`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
          "x-api-key": import.meta.env.VITE_X_API_KEY,
        },
        timeout: 30000,
      }
    );

    return response.data;
  } catch (error) {
    console.error("Error in geminiSpeechToTextApi:", error);
    return null;
  }
};

const geminiTextGeneartionApi = async (args: {
  userQuery: string;
  systemInstruction: string;
  jsonFormat: boolean;
}) => {
  try {
    const response = await axios.post(
      `${import.meta.env.VITE_BACKEND_URL}/gemini/text-generate`,
      args,
      {
        headers: {
          "x-api-key": import.meta.env.VITE_X_API_KEY,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error in geminiTextGeneartionApi:", error);
    return null;
  }
};

const sarvamApi = async ({
  fileUrl,
  mimeType,
  jsonFormat,
  languageCode,
  file,
}: {
  fileUrl: string;
  mimeType: string;
  jsonFormat: boolean;
  languageCode: string;
  file: File;
}) => {
  try {
    const formData = new FormData();
    const jsonData = {
      fileUrl: fileUrl,
      mimeType: mimeType,
      jsonFormat: jsonFormat,
      languageCode: languageCode,
    };
    formData.append("file", file);
    formData.append("data", JSON.stringify(jsonData));

    const response = await axios.post(
      `${import.meta.env.VITE_BACKEND_URL}/sarvam/speech-to-text`,
      formData,
      {
        headers: {
          "Content-Type": "multipart/form-data",
          "x-api-key": import.meta.env.VITE_X_API_KEY,
        },
      }
    );
    return response.data;
  } catch (error) {
    console.error("Error in sarvamApi:", error);
    return null;
  }
};
