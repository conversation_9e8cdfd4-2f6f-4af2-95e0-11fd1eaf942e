#!/usr/bin/env python3
"""
Clear cache and run main_with_cache.py
"""

from data_manager import DataManager
import subprocess
import sys

def clear_cache_and_run():
    """Clear cache and then run the main script."""
    print("🗑️ Clearing cache...")
    
    # Initialize data manager
    data_manager = DataManager(cache_dir="data_cache")
    
    # Clear cache
    success = data_manager.clear_cache()
    if success:
        print("✅ Cache cleared successfully")
    else:
        print("❌ Failed to clear cache")
        return
    
    print("🚀 Running main_with_cache.py...")
    
    # Run the main script
    try:
        subprocess.run([sys.executable, "main_with_cache.py"], check=True)
    except subprocess.CalledProcessError as e:
        print(f"❌ Error running main script: {e}")

if __name__ == "__main__":
    clear_cache_and_run()