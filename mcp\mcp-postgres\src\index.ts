import pg from "pg";
import { McpServer } from "@modelcontextprotocol/sdk/server/mcp.js";
import { StdioServerTransport } from "@modelcontextprotocol/sdk/server/stdio.js";
import dotenv from "dotenv";
import { z } from "zod";

// Load environment variables from .env file
dotenv.config();

const { Pool } = pg;

// Create a new pool using the connection string from environment variables
export const pool = new Pool({
  user: process.env.DATABASE_USER_NAME,
  password: process.env.DATABASE_PASSWORD,
  host: process.env.DATABASE_HOST,
  port: 5432,
  database: process.env.DATABASE_NAME,
});

// Create mcp server instance
const server = new McpServer({
  name: "postgres-mcp-server",
  version: "1.0.0",
  capabilities: {
    resources: {
     // postgresConnection: pool,
    },
    tools: {},
  },
});

server.tool(
  "get-function-names",
  "Get all function names in the PostgreSQL database",
  {},
  async () => {
    try {
      // Query to get function names from the PostgreSQL database
      const client = await pool.connect();
      
      // console.log("Database connected successfully!");
      const result = await client.query(`
        SELECT routine_name
        FROM information_schema.routines
        WHERE routine_schema = 'public' AND routine_type = 'FUNCTION'
      `);
      client.release();

      if (result.rows.length === 0) {
        return {
          content: [
            {
              type: "text",
              text: "No functions found in the database.",
            },
          ],
        };
      }

      // Extract function names from the result
      const functionNames = result.rows.map((row: any) => row.routine_name);

      return {
        content: [
          {
            type: "text",
            text: `Function names in the database:\n\n${functionNames.join(
              "\n"
            )}`,
          },
        ],
      };
    } catch (error) {
      console.error("Error fetching function names:", error);
      return {
        content: [
          {
            type: "text",
            text: "Failed to fetch function names from the database.",
          },
        ],
      };
    }
  }
);

server.tool(
  "get-all-roles",
  "Get all roles in the PostgreSQL database",
  {},
  async () => {
    try {
      // Query to get function names from the PostgreSQL database
      const client = await pool.connect();

      // Query to get all roles from the cl_cf_roles table
      const result = await client.query("SELECT * FROM cl_cf_roles");

      // Release the connection back to the pool
      client.release();

      // Check if any roles were found
      if (result.rows.length === 0) {
        return {
          content: [
            {
              type: "text",
              text: "No roles found in the database.",
            },
          ],
        };
      }

      // Extract the roles from the result
      const roles = result.rows.map((row) => row.role_code); // Assuming 'role_name' is the column name

      return {
        content: [
          {
            type: "text",
            text: `Roles in the database:\n\n${roles.join("\n")}`,
          },
        ],
      };
    } catch (error) {
      console.error("Error fetching roles:", error);
      return {
        content: [
          {
            type: "text",
            text: "Failed to fetch roles from the database.",
          },
        ],
      };
    }
  }
);

server.tool(
  "get-all-tables",
  "Get all tables present in the PostgreSQL database",
  {},
  async () => {
    // Access the resource here
    try {
      // Get client from the connection pool
      const client = await pool.connect();

      // Query to get the table names from the public schema
      const result = await client.query(`
        SELECT table_name 
        FROM information_schema.tables
        WHERE table_schema = 'public'
      `);

      client.release(); // Release the client back to the pool

      if (result.rows.length === 0) {
        return {
          content: [
            {
              type: "text",
              text: "No tables found in the database.",
            },
          ],
        };
      }

      // Extract the table names from the result
      const tableNames = result.rows.map((row) => row.table_name);

      return {
        content: [
          {
            type: "text",
            text: `Tables in the database:\n\n${tableNames.join("\n")}`,
          },
        ],
      };
    } catch (error) {
      console.error("Error fetching table names:", error);
      return {
        content: [
          {
            type: "text",
            text: "Failed to fetch table names from the database.",
          },
        ],
      };
    }
  }
);

server.tool(
  "get-data-from-table",
  "Get data from a specific table",
  {
    tableName: z
      .string()
      .describe("Exact name of the table (e.g., cl_cf_roles)"),
  },
  async ({ tableName }) => {
    // Access the tableName parameter
    try {
      // Validate if the table exists in the database
      const client = await pool.connect(); // Get client from the pool

      // Query to check if the table exists
      const result = await client.query(
        `
        SELECT EXISTS (
          SELECT 1
          FROM information_schema.tables
          WHERE table_schema = 'public' AND table_name = $1
        ) AS table_exists
      `,
        [tableName]
      );

      client.release(); // Release the client back to the pool

      if (!result.rows[0].table_exists) {
        return {
          content: [
            {
              type: "text",
              text: `Table "${tableName}" does not exist in the database.`,
            },
          ],
        };
      }

      // If table exists, fetch the data from the table
      const client2 = await pool.connect();
      const dataResult = await client2.query(`SELECT * FROM ${tableName}`);
      client2.release(); // Release the client back to the pool

      if (dataResult.rows.length === 0) {
        return {
          content: [
            {
              type: "text",
              text: `No data found in the table "${tableName}".`,
            },
          ],
        };
      }

      // Format the results for easy reading
      const formattedData = dataResult.rows
        .map((row) => JSON.stringify(row))
        .join("\n");

      return {
        content: [
          {
            type: "text",
            text: `Data from table "${tableName}":\n\n${formattedData}`,
          },
        ],
      };
    } catch (error) {
      console.error("Error fetching data from the table:", error);
      return {
        content: [
          {
            type: "text",
            text: `Failed to fetch data from table "${tableName}".`,
          },
        ],
      };
    }
  }
);

server.tool(
  "get-column-data",
  "Get data from a specific column in a specific table",
  {
    tableName: z
      .string()
      .describe("Exact name of the table (e.g., cl_cf_roles)"),
    columnName: z
      .string()
      .describe("Exact name of the column (e.g., role_code)"),
  },
  async ({ tableName, columnName }) => {
    // Access the tableName and columnName parameters
    try {
      // Validate if the table and column exist in the database
      const client = await pool.connect(); // Get client from the pool

      // Check if the table exists
      const tableCheckResult = await client.query(
        `
        SELECT EXISTS (
          SELECT 1
          FROM information_schema.tables
          WHERE table_schema = 'public' AND table_name = $1
        ) AS table_exists
      `,
        [tableName]
      );

      // Check if the column exists in the table
      const columnCheckResult = await client.query(
        `
        SELECT EXISTS (
          SELECT 1
          FROM information_schema.columns
          WHERE table_schema = 'public' AND table_name = $1 AND column_name = $2
        ) AS column_exists
      `,
        [tableName, columnName]
      );

      if (!tableCheckResult.rows[0].table_exists) {
        client.release();
        return {
          content: [
            {
              type: "text",
              text: `Table "${tableName}" does not exist in the database.`,
            },
          ],
        };
      }

      if (!columnCheckResult.rows[0].column_exists) {
        client.release();
        return {
          content: [
            {
              type: "text",
              text: `Column "${columnName}" does not exist in the table "${tableName}".`,
            },
          ],
        };
      }

      // If both table and column exist, fetch the column data
      const dataResult = await client.query(
        `SELECT ${columnName} FROM ${tableName}`
      );
      client.release(); // Release the client back to the pool

      if (dataResult.rows.length === 0) {
        return {
          content: [
            {
              type: "text",
              text: `No data found in the column "${columnName}" of the table "${tableName}".`,
            },
          ],
        };
      }

      // Format the results for easy reading
      const formattedData = dataResult.rows
        .map((row) => JSON.stringify(row))
        .join("\n");

      return {
        content: [
          {
            type: "text",
            text: `Data from column "${columnName}" in table "${tableName}":\n\n${formattedData}`,
          },
        ],
      };
    } catch (error) {
      console.error("Error fetching column data:", error);
      return {
        content: [
          {
            type: "text",
            text: `Failed to fetch data from column "${columnName}" in table "${tableName}".`,
          },
        ],
      };
    }
  }
);

// Function to run the MCP server
async function main() {
  const transport = new StdioServerTransport();
  await server.connect(transport); // Connect the server to stdio
}

// Start the server
main().catch((error) => {
  console.error("Fatal error in main():", error);
  process.exit(1);
});
