import { createBrowserRouter } from 'react-router-dom';
import RootLayout from '../layouts/RootLayout';
import RecordPage from '../pages/RecordPage';
import HistoryPage from '../pages/HistoryPage';

export const router = createBrowserRouter([
  {
    path: '/',
    element: <RootLayout />,
    children: [
      {
        index: true,
        element: <RecordPage />,
      },
      {
        path: 'history',
        element: <HistoryPage />,
      },
    ],
  },
]);