import { RecursiveCharacterTextSplitter } from "langchain/text_splitter";
import { MemoryVectorStore } from "langchain/vectorstores/memory";
import { OpenAIEmbeddings } from "@langchain/openai";

export const getDataSplit = async ({ data }) => {
  try {
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: 2000,
      chunkOverlap: 200,
    });
    const splits = await textSplitter.splitDocuments(data);

    return {
      status: true,
      data: { splits },
    };
  } catch (error) {
    console.log("vectorStore :: getDataSplit ::  error :: ", error?.message);

    return {
      status: false,
      errorMsg: error?.message,
    };
  }
};

export const getVectorStoreAndRetriever = async ({ splits }) => {
  try {
    const vectorStore = await MemoryVectorStore.fromDocuments(
      splits,
      new OpenAIEmbeddings({
        //model: "text-embedding-3-large",
      })
    );

    const retriever = vectorStore.asRetriever();

    return {
      status: true,
      data: {
        vectorStore,
        retriever,
      },
    };
  } catch (error) {
    console.log("vectorStore :: getVectorStoreAndRetriever ::  error :: ", error?.message);
    return {
      status: false,
      errorMsg: error?.message,
    };
  }
};
