import React from 'react';

const ProcessingIndicator: React.FC = () => {
  return (
    <div className="text-center">
      <div className="inline-flex items-center px-6 py-3 rounded-xl bg-white shadow-lg border border-gray-100">
        <div className="animate-spin mr-3 h-5 w-5 border-2 border-indigo-500 border-t-transparent rounded-full" />
        <span className="text-gray-700">Transcribing audio...</span>
      </div>
    </div>
  );
};

export default ProcessingIndicator;