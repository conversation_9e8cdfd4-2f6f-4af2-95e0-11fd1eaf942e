/*
  # Remove user_id requirement

  1. Changes
    - Drop user_id column from recordings table
    - Update RLS policies for public access

  2. Security
    - Enable RLS on recordings table
    - Add policy for public access to all operations
*/

-- Drop existing policies
DO $$ 
BEGIN
  DROP POLICY IF EXISTS "Users can read own recordings" ON recordings;
  DROP POLICY IF EXISTS "Users can create recordings" ON recordings;
  DROP POLICY IF EXISTS "Users can update own recordings" ON recordings;
  DROP POLICY IF EXISTS "Public can do anything" ON recordings;
EXCEPTION
  WHEN undefined_object THEN
    NULL;
END $$;

-- Remove user_id column
DO $$ 
BEGIN
  ALTER TABLE recordings DROP COLUMN IF EXISTS user_id;
EXCEPTION
  WHEN undefined_object THEN
    CREATE TABLE recordings (
      id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
      name text NOT NULL,
      audio_url text NOT NULL,
      transcription text,
      tone text,
      rating numeric,
      status text NOT NULL DEFAULT 'processing',
      created_at timestamptz DEFAULT now()
    );
END $$;

-- Enable RLS and create public policy
ALTER TABLE recordings ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Public can do anything"
  ON recordings
  FOR ALL
  USING (true)
  WITH CHECK (true);