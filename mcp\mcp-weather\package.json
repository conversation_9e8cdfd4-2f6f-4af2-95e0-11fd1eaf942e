{"name": "mcp-weather", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "bin": {"weather": "./build/index.js"}, "scripts": {"build": "tsc && chmod 755 build/index.js"}, "files": ["build"], "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@types/node": "^22.14.0", "typescript": "^5.8.3"}, "dependencies": {"@modelcontextprotocol/sdk": "^1.9.0", "dotenv": "^16.4.7", "zod": "^3.24.2"}}