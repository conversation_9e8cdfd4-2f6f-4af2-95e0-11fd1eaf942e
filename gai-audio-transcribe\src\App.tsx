import React, { useState } from 'react';
import AudioRecorder from './components/AudioRecorder';
import RecordingHistory from './components/RecordingHistory';
import Sidebar from './components/Sidebar';
import type { Recording } from './services/recordingService';

function App() {
  const [recordings, setRecordings] = useState<Recording[]>([]);
  const [activeSection, setActiveSection] = useState<'record' | 'history'>('record');

  const handleRecordingComplete = (recording: Recording) => {
    setRecordings(prev => {
      const index = prev.findIndex(r => r.id === recording.id);
      if (index >= 0) {
        // Update existing recording
        const newRecordings = [...prev];
        newRecordings[index] = recording;
        return newRecordings;
      }
      // Add new recording
      return [recording, ...prev];
    });
    
    if (recording.status === 'processing') {
      setActiveSection('history');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
      <Sidebar activeSection={activeSection} onSectionChange={setActiveSection} />
      
      <main className="ml-16 md:ml-64 min-h-screen">
        <div className="max-w-4xl mx-auto p-6 md:p-8">
          {activeSection === 'record' ? (
            <div className="space-y-8 animate-fadeIn">
              <div className="text-center">
                <h2 className="text-4xl font-bold text-gray-800 mb-3 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600">
                  Record Your Voice
                </h2>
                <p className="text-gray-600 text-lg">
                  Get instant transcription of your recording
                </p>
              </div>
              <AudioRecorder onRecordingComplete={handleRecordingComplete} />
            </div>
          ) : (
            <div className="space-y-6 animate-fadeIn">
              <div>
                <h2 className="text-3xl font-bold text-gray-800 mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-indigo-600">
                  Recording History
                </h2>
                <p className="text-gray-600 text-lg">
                  View and play your previous recordings
                </p>
              </div>
              <RecordingHistory recordings={recordings} />
            </div>
          )}
        </div>
      </main>
    </div>
  );
}