import React from 'react';

interface RecordingTimerProps {
  duration: number;
}

const RecordingTimer: React.FC<RecordingTimerProps> = ({ duration }) => {
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <div className="text-5xl font-mono text-gray-800 text-center font-bold tracking-wider">
      {formatDuration(duration)}
    </div>
  );
};

export default RecordingTimer;