import React from 'react';
import { Play, Pause, Volume2 } from 'lucide-react';

interface AudioPlayerProps {
  audioURL: string | null;
  isPlaying: boolean;
  onTogglePlayback: () => void;
  audioRef: React.RefObject<HTMLAudioElement>;
  onEnded: () => void;
}

const AudioPlayer: React.FC<AudioPlayerProps> = ({
  audioURL,
  isPlaying,
  onTogglePlayback,
  audioRef,
  onEnded,
}) => {
  if (!audioURL) return null;

  return (
    <div className="w-full bg-white rounded-xl p-4 shadow-lg border border-gray-100">
      <div className="flex items-center justify-center space-x-6">
        <button
          onClick={onTogglePlayback}
          className={`p-5 rounded-full transition-all transform hover:scale-105 ${
            isPlaying
              ? 'bg-indigo-500 text-white shadow-lg shadow-indigo-500/30'
              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
          }`}
        >
          {isPlaying ? (
            <Pause className="w-8 h-8" />
          ) : (
            <Play className="w-8 h-8" />
          )}
        </button>
        <Volume2 className="w-8 h-8 text-gray-400" />
      </div>
      <audio
        ref={audioRef}
        src={audioURL}
        onEnded={onEnded}
        className="hidden"
      />
    </div>
  );
};

export default AudioPlayer;