import React, { useState, useEffect } from 'react';
import { Search, Filter, Calendar, Clock, Mic, AlertCircle, CheckCircle, XCircle } from 'lucide-react';
import RecordingHistory from '../components/RecordingHistory';
import { fetchRecordings } from '../services/recordingService';
import type { Recording } from '../services/recordingService';

const HistoryPage: React.FC = () => {
  const [recordings, setRecordings] = useState<Recording[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filterStatus, setFilterStatus] = useState<'all' | 'completed' | 'processing' | 'error'>('all');

  useEffect(() => {
    loadRecordings();
  }, []); // Remove recordings dependency

  useEffect(() => {
    // Separate useEffect for polling processing recordings
    const pollingInterval = setInterval(() => {
      const hasProcessingRecordings = recordings.some(rec => rec.status === 'processing');
      if (hasProcessingRecordings) {
        loadRecordings();
      }
    }, 3000);

    return () => clearInterval(pollingInterval);
  }, [recordings]); // Keep recordings dependency for polling check

  const loadRecordings = async () => {
    try {
      setError(null);
      const data = await fetchRecordings();
      setRecordings(data);
      setIsLoading(false);
    } catch (err) {
      console.error('Error loading recordings:', err);
      setError('Failed to load recordings. Please try again.');
      setIsLoading(false);
    }
  };

  const filteredRecordings = recordings.filter(recording => {
    const matchesSearch = recording.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || recording.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const stats = {
    total: recordings.length,
    completed: recordings.filter(r => r.status === 'completed').length,
    processing: recordings.filter(r => r.status === 'processing').length,
    error: recordings.filter(r => r.status === 'error').length,
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-12 w-12 border-4 border-indigo-500 border-t-transparent rounded-full mb-4" />
          <p className="text-gray-600">Loading recordings...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-8 animate-fadeIn p-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-6">
        <div>
          <h2 className="text-4xl font-bold text-gray-800 mb-2 bg-clip-text text-transparent bg-gradient-to-r from-indigo-600 to-blue-600">
            Recording History
          </h2>
          <p className="text-gray-500 text-lg">
            Browse and manage your voice recordings
          </p>
        </div>

        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
            <input
              type="text"
              placeholder="Search recordings..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-12 pr-4 py-3 bg-white rounded-xl border border-gray-200 focus:ring-2 focus:ring-indigo-500 focus:border-transparent w-full md:w-64 transition-all"
            />
          </div>
          
          <div className="relative">
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value as any)}
              className="pl-4 pr-10 py-3 bg-white rounded-xl border border-gray-200 focus:ring-2 focus:ring-indigo-500 focus:border-transparent appearance-none cursor-pointer"
            >
              <option value="all">All Status</option>
              <option value="completed">Completed</option>
              <option value="processing">Processing</option>
              <option value="error">Error</option>
            </select>
            <Filter className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <span className="text-gray-600">Total Recordings</span>
            <div className="w-10 h-10 rounded-xl bg-indigo-100 flex items-center justify-center">
              <Mic className="w-5 h-5 text-indigo-600" />
            </div>
          </div>
          <p className="text-3xl font-bold text-gray-800">{stats.total}</p>
          <div className="mt-2 text-sm text-gray-500">All time recordings</div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <span className="text-gray-600">Completed</span>
            <div className="w-10 h-10 rounded-xl bg-green-100 flex items-center justify-center">
              <CheckCircle className="w-5 h-5 text-green-600" />
            </div>
          </div>
          <p className="text-3xl font-bold text-gray-800">{stats.completed}</p>
          <div className="mt-2 text-sm text-gray-500">Successfully processed</div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <span className="text-gray-600">Processing</span>
            <div className="w-10 h-10 rounded-xl bg-blue-100 flex items-center justify-center">
              <Clock className="w-5 h-5 text-blue-600" />
            </div>
          </div>
          <p className="text-3xl font-bold text-gray-800">{stats.processing}</p>
          <div className="mt-2 text-sm text-gray-500">Currently processing</div>
        </div>

        <div className="bg-white rounded-2xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow">
          <div className="flex items-center justify-between mb-4">
            <span className="text-gray-600">Failed</span>
            <div className="w-10 h-10 rounded-xl bg-red-100 flex items-center justify-center">
              <AlertCircle className="w-5 h-5 text-red-600" />
            </div>
          </div>
          <p className="text-3xl font-bold text-gray-800">{stats.error}</p>
          <div className="mt-2 text-sm text-gray-500">Processing failed</div>
        </div>
      </div>

      {error ? (
        <div className="bg-red-50 border border-red-200 rounded-xl p-6 text-center text-red-600">
          <AlertCircle className="w-8 h-8 mx-auto mb-3" />
          <p className="text-lg font-medium mb-4">{error}</p>
          <button
            onClick={loadRecordings}
            className="px-6 py-2 bg-red-100 hover:bg-red-200 rounded-lg transition-colors font-medium"
          >
            Try Again
          </button>
        </div>
      ) : (
        <div className="bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden">
          <RecordingHistory recordings={filteredRecordings} />
        </div>
      )}
    </div>
  );
};

export default HistoryPage;